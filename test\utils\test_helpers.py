"""
测试辅助函数

提供测试过程中常用的辅助函数和工具。
"""

import os
import json
import time
import tempfile
import shutil
import threading
from pathlib import Path
from typing import Dict, Any, List, Optional, Callable
from unittest.mock import Mock, patch


def create_temp_file(content: str = "", suffix: str = ".json", prefix: str = "test_") -> str:
    """创建临时文件"""
    fd, path = tempfile.mkstemp(suffix=suffix, prefix=prefix)
    try:
        with os.fdopen(fd, 'w', encoding='utf-8') as f:
            f.write(content)
    except:
        os.close(fd)
        raise
    return path


def create_temp_dir(prefix: str = "test_") -> str:
    """创建临时目录"""
    return tempfile.mkdtemp(prefix=prefix)


def cleanup_temp_path(path: str):
    """清理临时路径"""
    if os.path.exists(path):
        if os.path.isfile(path):
            os.remove(path)
        else:
            shutil.rmtree(path, ignore_errors=True)


def create_test_json_file(data: Dict[str, Any], file_path: str):
    """创建测试JSON文件"""
    os.makedirs(os.path.dirname(file_path), exist_ok=True)
    with open(file_path, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)


def read_json_file(file_path: str) -> Dict[str, Any]:
    """读取JSON文件"""
    with open(file_path, 'r', encoding='utf-8') as f:
        return json.load(f)


def wait_for_condition(condition: Callable[[], bool], timeout: float = 10.0, interval: float = 0.1) -> bool:
    """等待条件满足"""
    start_time = time.time()
    while time.time() - start_time < timeout:
        if condition():
            return True
        time.sleep(interval)
    return False


def wait_for_file_exists(file_path: str, timeout: float = 10.0) -> bool:
    """等待文件存在"""
    return wait_for_condition(lambda: os.path.exists(file_path), timeout)


def wait_for_file_content(file_path: str, expected_content: str, timeout: float = 10.0) -> bool:
    """等待文件包含指定内容"""
    def check_content():
        try:
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    return expected_content in f.read()
        except:
            pass
        return False
    
    return wait_for_condition(check_content, timeout)


def count_files_in_directory(directory: str, pattern: str = "*") -> int:
    """统计目录中的文件数量"""
    if not os.path.exists(directory):
        return 0
    
    from glob import glob
    return len(glob(os.path.join(directory, pattern)))


def get_file_modification_time(file_path: str) -> float:
    """获取文件修改时间"""
    if os.path.exists(file_path):
        return os.path.getmtime(file_path)
    return 0.0


def assert_json_equal(actual: Dict[str, Any], expected: Dict[str, Any], ignore_keys: List[str] = None):
    """断言JSON数据相等（可忽略指定键）"""
    if ignore_keys:
        actual = {k: v for k, v in actual.items() if k not in ignore_keys}
        expected = {k: v for k, v in expected.items() if k not in ignore_keys}
    
    assert actual == expected, f"JSON数据不匹配:\n实际: {actual}\n期望: {expected}"


def assert_file_contains_json(file_path: str, expected_data: Dict[str, Any], ignore_keys: List[str] = None):
    """断言文件包含指定的JSON数据"""
    assert os.path.exists(file_path), f"文件不存在: {file_path}"
    
    actual_data = read_json_file(file_path)
    assert_json_equal(actual_data, expected_data, ignore_keys)


def mock_serial_response(command: bytes, response: bytes):
    """创建串口响应的Mock"""
    def side_effect(data):
        if data == command:
            return len(data)
        return 0
    
    serial_mock = Mock()
    serial_mock.is_open = True
    serial_mock.write.side_effect = side_effect
    serial_mock.read.return_value = response
    return serial_mock


def create_mock_logger():
    """创建Mock日志对象"""
    logger = Mock()
    logger.debug = Mock()
    logger.info = Mock()
    logger.warning = Mock()
    logger.error = Mock()
    logger.critical = Mock()
    return logger


def patch_config_loader(config_data: Dict[str, Any]):
    """Patch配置加载器"""
    return patch('utils.config_loader.load_config', return_value=config_data)


def patch_logger(logger_name: str = "test_logger"):
    """Patch日志系统"""
    mock_logger = create_mock_logger()
    return patch('utils.logger.get_logger', return_value=mock_logger)


def run_with_timeout(func: Callable, timeout: float = 10.0, *args, **kwargs):
    """在超时时间内运行函数"""
    result = [None]
    exception = [None]
    
    def target():
        try:
            result[0] = func(*args, **kwargs)
        except Exception as e:
            exception[0] = e
    
    thread = threading.Thread(target=target)
    thread.daemon = True
    thread.start()
    thread.join(timeout)
    
    if thread.is_alive():
        raise TimeoutError(f"函数执行超时 ({timeout}秒)")
    
    if exception[0]:
        raise exception[0]
    
    return result[0]


def simulate_file_creation(file_path: str, content: str, delay: float = 0.0):
    """模拟文件创建过程"""
    def create_file():
        if delay > 0:
            time.sleep(delay)
        
        # 确保目录存在
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        
        # 创建临时文件，然后重命名（模拟原子操作）
        temp_path = file_path + ".tmp"
        with open(temp_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        os.rename(temp_path, file_path)
    
    thread = threading.Thread(target=create_file)
    thread.daemon = True
    thread.start()
    return thread


def measure_execution_time(func: Callable, *args, **kwargs) -> tuple:
    """测量函数执行时间"""
    start_time = time.time()
    result = func(*args, **kwargs)
    end_time = time.time()
    execution_time = end_time - start_time
    return result, execution_time


def generate_test_device_id(prefix: str = "TEST") -> str:
    """生成测试设备ID"""
    import uuid
    return f"{prefix}_{uuid.uuid4().hex[:8].upper()}"


def create_test_directory_structure(base_path: str, structure: Dict[str, Any]):
    """创建测试目录结构"""
    for name, content in structure.items():
        path = os.path.join(base_path, name)
        
        if isinstance(content, dict):
            # 子目录
            os.makedirs(path, exist_ok=True)
            create_test_directory_structure(path, content)
        elif isinstance(content, str):
            # 文件
            os.makedirs(os.path.dirname(path), exist_ok=True)
            with open(path, 'w', encoding='utf-8') as f:
                f.write(content)
        else:
            # 空目录
            os.makedirs(path, exist_ok=True)


def assert_log_contains(mock_logger: Mock, level: str, message: str):
    """断言日志包含指定消息"""
    log_method = getattr(mock_logger, level.lower())
    calls = log_method.call_args_list
    
    for call in calls:
        args, kwargs = call
        if args and message in str(args[0]):
            return True
    
    raise AssertionError(f"日志中未找到 {level} 级别的消息: {message}")


def reset_mock_calls(mock_obj: Mock):
    """重置Mock对象的调用记录"""
    mock_obj.reset_mock()
    for attr_name in dir(mock_obj):
        attr = getattr(mock_obj, attr_name)
        if isinstance(attr, Mock):
            attr.reset_mock()


class TemporaryEnvironment:
    """临时环境上下文管理器"""
    
    def __init__(self, env_vars: Dict[str, str]):
        self.env_vars = env_vars
        self.original_values = {}
    
    def __enter__(self):
        for key, value in self.env_vars.items():
            self.original_values[key] = os.environ.get(key)
            os.environ[key] = value
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        for key in self.env_vars:
            if self.original_values[key] is None:
                os.environ.pop(key, None)
            else:
                os.environ[key] = self.original_values[key]
