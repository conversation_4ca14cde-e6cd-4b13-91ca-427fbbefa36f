#!/usr/bin/env python3
"""
测试数据目录结构创建脚本
确保测试环境的目录结构完整
"""

import os
import json
from datetime import datetime

def create_test_directories():
    """创建测试数据目录结构"""
    
    # 获取test_data目录的绝对路径
    base_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 定义目录结构
    directories = [
        # 传感器数据目录
        "sensors/current",
        "sensors/history",
        
        # 设备数据目录
        "devices/current", 
        "devices/history",
        
        # 设备自检数据
        "check/pending",
        "check/uploaded",
        
        # 蚊虫检测数据
        "mosquito_shared/incoming",
        "mosquito_shared/processed", 
        "mosquito_shared/failed",
        
        # 日志目录
        "logs",
        
        # 报告目录
        "reports/html",
        "reports/json",
        "reports/pdf",
        
        # 模拟数据目录
        "mock_data",
        
        # 临时文件目录
        "temp/uploads",
        "temp/processing"
    ]
    
    # 创建目录
    created_dirs = []
    for dir_path in directories:
        full_path = os.path.join(base_dir, dir_path)
        try:
            os.makedirs(full_path, exist_ok=True)
            created_dirs.append(dir_path)
            print(f"✅ 创建目录: {dir_path}")
        except Exception as e:
            print(f"❌ 创建目录失败 {dir_path}: {e}")
    
    # 创建初始化文件
    create_initial_files(base_dir)
    
    print(f"\n🎉 测试数据目录结构创建完成！")
    print(f"📁 基础目录: {base_dir}")
    print(f"📊 创建了 {len(created_dirs)} 个目录")
    
    return True

def create_initial_files(base_dir):
    """创建初始化文件"""
    
    # 创建空的JSON文件
    json_files = [
        "sensors/current/sensor_data.json",
        "sensors/current/sensor_errors.json", 
        "sensors/history/sensor_data_archive.json",
        "sensors/history/sensor_errors_archive.json",
        "devices/current/co2_errors.json",
        "devices/current/device_status.json",
        "devices/history/co2_errors_archive.json",
        "mock_data/mqtt_messages.json",
        "mock_data/server_responses.json",
        "mock_data/test_scenarios.json"
    ]
    
    for json_file in json_files:
        file_path = os.path.join(base_dir, json_file)
        try:
            if not os.path.exists(file_path):
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump([], f, ensure_ascii=False, indent=2)
                print(f"📄 创建文件: {json_file}")
        except Exception as e:
            print(f"❌ 创建文件失败 {json_file}: {e}")
    
    # 创建.gitkeep文件确保空目录被git跟踪
    gitkeep_dirs = [
        "check/pending",
        "check/uploaded", 
        "mosquito_shared/incoming",
        "mosquito_shared/processed",
        "mosquito_shared/failed",
        "temp/uploads",
        "temp/processing"
    ]
    
    for gitkeep_dir in gitkeep_dirs:
        gitkeep_path = os.path.join(base_dir, gitkeep_dir, ".gitkeep")
        try:
            if not os.path.exists(gitkeep_path):
                with open(gitkeep_path, 'w') as f:
                    f.write("# 保持目录结构\n")
        except Exception as e:
            print(f"❌ 创建.gitkeep失败 {gitkeep_dir}: {e}")
    
    # 创建测试配置状态文件
    status_file = os.path.join(base_dir, "test_status.json")
    status_data = {
        "created_at": datetime.now().isoformat(),
        "version": "1.0.0",
        "directories_created": True,
        "initial_files_created": True,
        "ready_for_testing": True
    }
    
    try:
        with open(status_file, 'w', encoding='utf-8') as f:
            json.dump(status_data, f, ensure_ascii=False, indent=2)
        print(f"📋 创建状态文件: test_status.json")
    except Exception as e:
        print(f"❌ 创建状态文件失败: {e}")

if __name__ == "__main__":
    create_test_directories()
