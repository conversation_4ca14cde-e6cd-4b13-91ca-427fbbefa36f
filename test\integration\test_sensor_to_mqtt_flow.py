"""
传感器到MQTT数据流集成测试

测试从传感器数据收集到MQTT发布的完整流程。
"""

import pytest
import time
import threading
from unittest.mock import Mock, patch
from test.fixtures.mock_sensors import create_mock_sensor_collector
from test.fixtures.mock_mqtt import MockMQTTClient
from test.fixtures.test_data import TestDataGenerator
from test.fixtures.config_samples import get_test_config
from test.utils.test_helpers import wait_for_condition


@pytest.mark.integration
class TestSensorToMQTTFlow:
    """传感器到MQTT数据流集成测试类"""
    
    def setup_method(self):
        """测试前设置"""
        self.config = get_test_config("base")
        self.mock_logger = Mock()
        
    def test_sensor_data_collection_and_mqtt_publish(self):
        """测试传感器数据收集和MQTT发布集成"""
        # 创建Mock传感器收集器
        sensor_collector = create_mock_sensor_collector(self.config)
        
        # 创建Mock MQTT客户端
        mqtt_client = MockMQTTClient(self.config['mqtt']['device_id'])
        mqtt_client.connect()
        
        # 模拟数据收集
        test_data = TestDataGenerator.generate_sensor_data()
        sensor_collector.collect_data.return_value = test_data
        
        # 执行数据收集和发布
        collected_data = sensor_collector.collect_data()
        publish_result = mqtt_client.publish_sensor_data(collected_data)
        
        # 验证结果
        assert collected_data is not None
        assert publish_result is True
        
        # 检查发布的消息
        published_messages = mqtt_client.get_published_messages("sensor_data")
        assert len(published_messages) == 1
        assert published_messages[0]["data"] == collected_data
    
    def test_error_handling_in_data_flow(self):
        """测试数据流中的错误处理"""
        # 创建Mock传感器收集器（模拟错误）
        sensor_collector = create_mock_sensor_collector(self.config)
        sensor_collector.collect_data.return_value = None  # 模拟收集失败
        
        # 创建Mock MQTT客户端
        mqtt_client = MockMQTTClient(self.config['mqtt']['device_id'])
        mqtt_client.connect()
        
        # 执行数据收集
        collected_data = sensor_collector.collect_data()
        
        # 验证错误处理
        assert collected_data is None
        
        # 应该不会发布空数据
        if collected_data is None:
            # 不发布数据
            published_messages = mqtt_client.get_published_messages("sensor_data")
            assert len(published_messages) == 0
    
    def test_mqtt_connection_failure_handling(self):
        """测试MQTT连接失败处理"""
        # 创建Mock传感器收集器
        sensor_collector = create_mock_sensor_collector(self.config)
        test_data = TestDataGenerator.generate_sensor_data()
        sensor_collector.collect_data.return_value = test_data
        
        # 创建Mock MQTT客户端（未连接）
        mqtt_client = MockMQTTClient(self.config['mqtt']['device_id'])
        # 不调用connect()，保持未连接状态
        
        # 尝试发布数据
        collected_data = sensor_collector.collect_data()
        publish_result = mqtt_client.publish_sensor_data(collected_data)
        
        # 验证结果
        assert collected_data is not None
        assert publish_result is False  # 发布应该失败
    
    def test_batch_data_processing(self):
        """测试批量数据处理"""
        # 创建Mock传感器收集器
        sensor_collector = create_mock_sensor_collector(self.config)
        
        # 创建Mock MQTT客户端
        mqtt_client = MockMQTTClient(self.config['mqtt']['device_id'])
        mqtt_client.connect()
        
        # 生成批量测试数据
        batch_data = TestDataGenerator.generate_batch_sensor_data(5)
        
        # 模拟批量数据收集和发布
        published_count = 0
        for data in batch_data:
            sensor_collector.collect_data.return_value = data
            collected_data = sensor_collector.collect_data()
            
            if collected_data:
                result = mqtt_client.publish_sensor_data(collected_data)
                if result:
                    published_count += 1
        
        # 验证结果
        assert published_count == 5
        
        # 检查发布的消息
        published_messages = mqtt_client.get_published_messages("sensor_data")
        assert len(published_messages) == 5
    
    def test_concurrent_data_collection(self):
        """测试并发数据收集"""
        # 创建多个Mock传感器收集器
        collectors = []
        mqtt_clients = []
        
        for i in range(3):
            collector = create_mock_sensor_collector(self.config)
            mqtt_client = MockMQTTClient(f"TEST_DEVICE_{i:03d}")
            mqtt_client.connect()
            
            collectors.append(collector)
            mqtt_clients.append(mqtt_client)
        
        # 并发执行数据收集和发布
        def collect_and_publish(collector, mqtt_client):
            for _ in range(5):
                test_data = TestDataGenerator.generate_sensor_data()
                collector.collect_data.return_value = test_data
                
                collected_data = collector.collect_data()
                if collected_data:
                    mqtt_client.publish_sensor_data(collected_data)
                
                time.sleep(0.1)  # 模拟采集间隔
        
        # 启动并发线程
        threads = []
        for collector, mqtt_client in zip(collectors, mqtt_clients):
            thread = threading.Thread(
                target=collect_and_publish,
                args=(collector, mqtt_client)
            )
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join(timeout=10)
        
        # 验证结果
        total_published = 0
        for mqtt_client in mqtt_clients:
            messages = mqtt_client.get_published_messages("sensor_data")
            total_published += len(messages)
        
        assert total_published == 15  # 3个设备 × 5条消息
    
    def test_data_validation_in_flow(self):
        """测试数据流中的数据验证"""
        # 创建Mock传感器收集器
        sensor_collector = create_mock_sensor_collector(self.config)
        
        # 创建Mock MQTT客户端
        mqtt_client = MockMQTTClient(self.config['mqtt']['device_id'])
        mqtt_client.connect()
        
        # 测试有效数据
        valid_data = TestDataGenerator.generate_sensor_data()
        sensor_collector.collect_data.return_value = valid_data
        
        collected_data = sensor_collector.collect_data()
        
        # 验证数据格式
        assert self._validate_sensor_data(collected_data)
        
        # 发布有效数据
        result = mqtt_client.publish_sensor_data(collected_data)
        assert result is True
        
        # 测试无效数据
        invalid_data = TestDataGenerator.generate_invalid_sensor_data()
        sensor_collector.collect_data.return_value = invalid_data
        
        collected_data = sensor_collector.collect_data()
        
        # 验证数据格式（应该无效）
        assert not self._validate_sensor_data(collected_data)
    
    def test_error_data_reporting(self):
        """测试错误数据上报"""
        # 创建Mock传感器收集器
        sensor_collector = create_mock_sensor_collector(self.config)
        
        # 创建Mock MQTT客户端
        mqtt_client = MockMQTTClient(self.config['mqtt']['device_id'])
        mqtt_client.connect()
        
        # 模拟传感器错误
        sensor_collector.collect_data.return_value = None
        
        # 生成错误数据
        error_data = TestDataGenerator.generate_error_data(
            sensor_type="cth",
            error_type="通信错误",
            error_message="传感器无响应"
        )
        
        # 发布错误数据
        result = mqtt_client.publish_error_data(error_data)
        assert result is True
        
        # 检查发布的错误消息
        error_messages = mqtt_client.get_published_messages("error_data")
        assert len(error_messages) == 1
        assert error_messages[0]["data"]["sensor_type"] == "cth"
        assert error_messages[0]["data"]["error_type"] == "通信错误"
    
    def test_network_resilience(self):
        """测试网络弹性"""
        # 创建Mock传感器收集器
        sensor_collector = create_mock_sensor_collector(self.config)
        
        # 创建Mock MQTT客户端
        mqtt_client = MockMQTTClient(self.config['mqtt']['device_id'])
        mqtt_client.connect()
        
        # 设置网络不稳定（50%成功率）
        mqtt_client.set_publish_success_rate(0.5)
        
        # 尝试发布多条数据
        success_count = 0
        failure_count = 0
        
        for _ in range(20):
            test_data = TestDataGenerator.generate_sensor_data()
            sensor_collector.collect_data.return_value = test_data
            
            collected_data = sensor_collector.collect_data()
            result = mqtt_client.publish_sensor_data(collected_data)
            
            if result:
                success_count += 1
            else:
                failure_count += 1
        
        # 验证网络不稳定情况下的行为
        assert success_count > 0  # 应该有成功的发布
        assert failure_count > 0  # 应该有失败的发布
        assert success_count + failure_count == 20
    
    def _validate_sensor_data(self, data):
        """验证传感器数据格式"""
        if not isinstance(data, dict):
            return False
        
        required_fields = ['timestamp', 'datetime', 'temperature', 'humidity', 'co2']
        for field in required_fields:
            if field not in data:
                return False
        
        # 验证数据类型和范围
        try:
            timestamp = data['timestamp']
            if not isinstance(timestamp, (int, float)) or timestamp <= 0:
                return False
            
            temperature = data['temperature']
            if not isinstance(temperature, (int, float)):
                return False
            
            humidity = data['humidity']
            if not isinstance(humidity, (int, float)) or humidity < 0 or humidity > 100:
                return False
            
            co2 = data['co2']
            if not isinstance(co2, int) or co2 < 0:
                return False
            
            return True
            
        except (KeyError, TypeError, ValueError):
            return False
