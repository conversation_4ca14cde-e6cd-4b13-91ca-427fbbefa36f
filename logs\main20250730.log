2025-07-30 11:14:03,806 - main - INFO - 初始化传感器系统...
2025-07-30 11:14:03,807 - main - INFO - 加载配置文件...
2025-07-30 11:14:03,841 - main - INFO - 配置文件加载成功
2025-07-30 11:14:03,841 - main - INFO - 🧪 测试模式已启用
2025-07-30 11:14:03,841 - main - INFO - 🧪 测试模式：跳过GPIO初始化
2025-07-30 11:14:03,841 - main - INFO - 🧪 初始化模拟MQTT客户端...
2025-07-30 11:14:03,849 - main - INFO - 初始化传感器收集器...
2025-07-30 11:14:03,890 - main - INFO - 🧪 测试模式：跳过CO2控制器初始化
2025-07-30 11:14:03,890 - main - INFO - 🧪 测试模式：跳过设备自检系统初始化
2025-07-30 11:14:03,890 - main - INFO - 初始化蚊子检测文件监控...
2025-07-30 11:14:03,927 - main - INFO - 蚊子检测文件监控初始化完成
2025-07-30 11:14:03,928 - main - INFO - 内存监控已启动
2025-07-30 11:14:03,929 - main - INFO - 线程管理器已启动
2025-07-30 11:14:03,929 - main - INFO - 异步文件I/O已启动
2025-07-30 11:14:03,930 - main - INFO - 系统初始化完成
2025-07-30 11:14:03,930 - main - INFO - 
=== 启动传感器数据采集与上传系统 ===

2025-07-30 11:14:03,930 - main - INFO - 启动MQTT上传线程...
2025-07-30 11:14:03,930 - main - INFO - 🧪 测试模式：跳过MQTT连接等待，立即继续启动
2025-07-30 11:14:03,930 - main - INFO - 启动传感器收集线程...
2025-07-30 11:14:03,931 - main - INFO - 传感器收集线程已启动，数据采集间隔为60秒
2025-07-30 11:14:05,934 - main - INFO - 🧪 测试模式：跳过CO2控制器启动
2025-07-30 11:14:05,934 - main - INFO - 等待传感器数据采集完成第一个周期...
2025-07-30 11:14:10,940 - main - INFO - 🧪 测试模式：跳过设备自检系统启动
2025-07-30 11:14:10,940 - main - INFO - 启动蚊子检测文件监控...
2025-07-30 11:14:10,941 - main - INFO - 设备开机状态已在MQTT连接建立时自动上报，无需重复发送
2025-07-30 11:14:10,941 - main - INFO - 
系统已成功启动，按 Ctrl+C 停止
2025-07-30 11:19:11,458 - main - INFO - 系统运行正常，MQTT连接已建立
2025-07-30 11:21:32,447 - main - INFO - 
正在停止系统...
2025-07-30 11:21:32,448 - main - INFO - 上报设备关机状态...
2025-07-30 11:21:32,949 - main - INFO - 停止传感器收集线程...
2025-07-30 11:21:33,436 - main - INFO - 执行最终数据归档...
2025-07-30 11:21:33,436 - main - INFO - 最终数据归档完成
2025-07-30 11:21:33,436 - main - INFO - 停止MQTT客户端...
2025-07-30 11:21:33,476 - main - INFO - 停止蚊子检测文件监控...
2025-07-30 11:21:36,393 - main - INFO - 停止内存监控...
2025-07-30 11:21:41,394 - main - INFO - 停止异步文件I/O...
2025-07-30 11:21:43,339 - main - INFO - 停止线程管理器...
2025-07-30 11:39:56,589 - main - INFO - 初始化传感器系统...
2025-07-30 11:39:56,589 - main - INFO - 加载配置文件...
2025-07-30 11:39:56,623 - main - INFO - 配置文件加载成功
2025-07-30 11:39:56,623 - main - INFO - 🧪 测试模式已启用
2025-07-30 11:39:56,624 - main - INFO - 🧪 测试模式：跳过GPIO初始化
2025-07-30 11:39:56,624 - main - INFO - 🧪 初始化模拟MQTT客户端...
2025-07-30 11:39:56,635 - main - INFO - 初始化传感器收集器...
2025-07-30 11:39:56,675 - main - INFO - 🧪 测试模式：跳过CO2控制器初始化
2025-07-30 11:39:56,675 - main - INFO - 🧪 测试模式：跳过设备自检系统初始化
2025-07-30 11:39:56,675 - main - INFO - 初始化蚊子检测文件监控...
2025-07-30 11:39:56,713 - main - INFO - 蚊子检测文件监控初始化完成
2025-07-30 11:39:56,715 - main - INFO - 内存监控已启动
2025-07-30 11:39:56,716 - main - INFO - 线程管理器已启动
2025-07-30 11:39:56,716 - main - INFO - 异步文件I/O已启动
2025-07-30 11:39:56,716 - main - INFO - 系统初始化完成
2025-07-30 11:39:56,716 - main - INFO - 
=== 启动传感器数据采集与上传系统 ===

2025-07-30 11:39:56,717 - main - INFO - 启动MQTT上传线程...
2025-07-30 11:39:56,717 - main - INFO - 🧪 测试模式：跳过MQTT连接等待，立即继续启动
2025-07-30 11:39:56,717 - main - INFO - 启动传感器收集线程...
2025-07-30 11:39:56,719 - main - INFO - 传感器收集线程已启动，数据采集间隔为60秒
2025-07-30 11:39:58,721 - main - INFO - 🧪 测试模式：跳过CO2控制器启动
2025-07-30 11:39:58,722 - main - INFO - 等待传感器数据采集完成第一个周期...
2025-07-30 11:40:03,727 - main - INFO - 🧪 测试模式：跳过设备自检系统启动
2025-07-30 11:40:03,727 - main - INFO - 启动蚊子检测文件监控...
2025-07-30 11:40:03,728 - main - INFO - 设备开机状态已在MQTT连接建立时自动上报，无需重复发送
2025-07-30 11:40:03,729 - main - INFO - 
系统已成功启动，按 Ctrl+C 停止
2025-07-30 11:41:21,797 - main - INFO - 
正在停止系统...
2025-07-30 11:41:21,798 - main - INFO - 上报设备关机状态...
2025-07-30 11:41:22,298 - main - INFO - 停止传感器收集线程...
2025-07-30 11:41:22,737 - main - INFO - 执行最终数据归档...
2025-07-30 11:41:22,737 - main - INFO - 最终数据归档完成
2025-07-30 11:41:22,737 - main - INFO - 停止MQTT客户端...
2025-07-30 11:41:22,780 - main - INFO - 停止蚊子检测文件监控...
2025-07-30 11:41:23,796 - main - INFO - 停止内存监控...
2025-07-30 11:41:26,783 - main - INFO - 停止异步文件I/O...
2025-07-30 11:41:29,735 - main - INFO - 停止线程管理器...
2025-07-30 12:02:55,261 - main - INFO - 初始化传感器系统...
2025-07-30 12:02:55,261 - main - INFO - 加载配置文件...
2025-07-30 12:02:55,295 - main - INFO - 配置文件加载成功
2025-07-30 12:02:55,295 - main - INFO - 🧪 测试模式已启用
2025-07-30 12:02:55,296 - main - INFO - 🧪 测试模式：跳过GPIO初始化
2025-07-30 12:02:55,296 - main - INFO - 🧪 初始化模拟MQTT客户端...
2025-07-30 12:02:55,305 - main - INFO - 初始化传感器收集器...
2025-07-30 12:02:55,346 - main - INFO - 🧪 测试模式：跳过CO2控制器初始化
2025-07-30 12:02:55,347 - main - INFO - 🧪 测试模式：跳过设备自检系统初始化
2025-07-30 12:02:55,347 - main - INFO - 初始化蚊子检测文件监控...
2025-07-30 12:02:55,352 - main - INFO - 蚊子检测文件监控初始化完成
2025-07-30 12:02:55,355 - main - INFO - 内存监控已启动
2025-07-30 12:02:55,356 - main - INFO - 线程管理器已启动
2025-07-30 12:02:55,357 - main - INFO - 异步文件I/O已启动
2025-07-30 12:02:55,357 - main - INFO - 系统初始化完成
2025-07-30 12:02:55,357 - main - INFO - 
=== 启动传感器数据采集与上传系统 ===

2025-07-30 12:02:55,357 - main - INFO - 启动MQTT上传线程...
2025-07-30 12:02:55,357 - main - INFO - 🧪 测试模式：跳过MQTT连接等待，立即继续启动
2025-07-30 12:02:55,357 - main - INFO - 启动传感器收集线程...
2025-07-30 12:02:55,359 - main - INFO - 传感器收集线程已启动，数据采集间隔为60秒
2025-07-30 12:02:57,361 - main - INFO - 🧪 测试模式：跳过CO2控制器启动
2025-07-30 12:02:57,362 - main - INFO - 等待传感器数据采集完成第一个周期...
2025-07-30 12:03:02,367 - main - INFO - 🧪 测试模式：跳过设备自检系统启动
2025-07-30 12:03:02,368 - main - INFO - 启动蚊子检测文件监控...
2025-07-30 12:03:02,369 - main - INFO - 设备开机状态已在MQTT连接建立时自动上报，无需重复发送
2025-07-30 12:03:02,369 - main - INFO - 
系统已成功启动，按 Ctrl+C 停止
2025-07-30 12:04:55,225 - main - INFO - 
正在停止系统...
2025-07-30 12:04:55,225 - main - INFO - 上报设备关机状态...
2025-07-30 12:04:55,726 - main - INFO - 停止传感器收集线程...
2025-07-30 12:04:56,438 - main - INFO - 执行最终数据归档...
2025-07-30 12:04:56,439 - main - INFO - 最终数据归档完成
2025-07-30 12:04:56,439 - main - INFO - 停止MQTT客户端...
2025-07-30 12:04:56,521 - main - INFO - 停止蚊子检测文件监控...
2025-07-30 12:04:57,674 - main - INFO - 停止内存监控...
2025-07-30 12:05:02,675 - main - INFO - 停止异步文件I/O...
2025-07-30 12:05:04,426 - main - INFO - 停止线程管理器...
2025-07-30 12:10:03,786 - main - INFO - 初始化传感器系统...
2025-07-30 12:10:03,787 - main - INFO - 加载配置文件...
2025-07-30 12:10:03,821 - main - INFO - 配置文件加载成功
2025-07-30 12:10:03,821 - main - INFO - 🧪 测试模式已启用
2025-07-30 12:10:03,821 - main - INFO - 🧪 测试模式：跳过GPIO初始化
2025-07-30 12:10:03,822 - main - INFO - 🧪 初始化模拟MQTT客户端...
2025-07-30 12:10:03,824 - main - INFO - 初始化传感器收集器...
2025-07-30 12:10:03,849 - main - INFO - 🧪 测试模式：跳过CO2控制器初始化
2025-07-30 12:10:03,849 - main - INFO - 🧪 测试模式：跳过设备自检系统初始化
2025-07-30 12:10:03,849 - main - INFO - 初始化蚊子检测文件监控...
2025-07-30 12:10:03,851 - main - INFO - 蚊子检测文件监控初始化完成
2025-07-30 12:10:03,852 - main - INFO - 内存监控已启动
2025-07-30 12:10:03,853 - main - INFO - 线程管理器已启动
2025-07-30 12:10:03,854 - main - INFO - 异步文件I/O已启动
2025-07-30 12:10:03,854 - main - INFO - 系统初始化完成
2025-07-30 12:10:03,854 - main - INFO - 
=== 启动传感器数据采集与上传系统 ===

2025-07-30 12:10:03,854 - main - INFO - 启动MQTT上传线程...
2025-07-30 12:10:03,855 - main - INFO - 🧪 测试模式：跳过MQTT连接等待，立即继续启动
2025-07-30 12:10:03,855 - main - INFO - 启动传感器收集线程...
2025-07-30 12:10:03,856 - main - INFO - 传感器收集线程已启动，数据采集间隔为60秒
2025-07-30 12:10:05,858 - main - INFO - 🧪 测试模式：跳过CO2控制器启动
2025-07-30 12:10:05,858 - main - INFO - 等待传感器数据采集完成第一个周期...
2025-07-30 12:10:10,864 - main - INFO - 🧪 测试模式：跳过设备自检系统启动
2025-07-30 12:10:10,864 - main - INFO - 启动蚊子检测文件监控...
2025-07-30 12:10:10,865 - main - INFO - 设备开机状态已在MQTT连接建立时自动上报，无需重复发送
2025-07-30 12:10:10,865 - main - INFO - 
系统已成功启动，按 Ctrl+C 停止
2025-07-30 12:15:11,355 - main - INFO - 系统运行正常，MQTT连接已建立
2025-07-30 12:15:43,895 - main - INFO - 
正在停止系统...
2025-07-30 12:15:43,895 - main - INFO - 上报设备关机状态...
2025-07-30 12:15:44,396 - main - INFO - 停止传感器收集线程...
2025-07-30 12:15:45,195 - main - INFO - 执行最终数据归档...
2025-07-30 12:15:45,195 - main - INFO - 最终数据归档完成
2025-07-30 12:15:45,195 - main - INFO - 停止MQTT客户端...
2025-07-30 12:15:45,223 - main - INFO - 停止蚊子检测文件监控...
2025-07-30 12:15:46,960 - main - INFO - 停止内存监控...
2025-07-30 12:15:51,961 - main - INFO - 停止异步文件I/O...
2025-07-30 12:15:52,163 - main - INFO - 停止线程管理器...
2025-07-30 12:16:04,182 - main - INFO - 所有组件已成功停止
2025-07-30 12:16:04,183 - main - INFO - 🧪 测试模式：跳过GPIO清理
2025-07-30 12:16:04,330 - main - INFO - 🧪 测试API服务器已停止
2025-07-30 12:16:04,330 - main - INFO - 系统已完全停止
2025-07-30 12:58:49,814 - main - INFO - 初始化传感器系统...
2025-07-30 12:58:49,814 - main - INFO - 加载配置文件...
2025-07-30 12:58:49,848 - main - INFO - 配置文件加载成功
2025-07-30 12:58:49,848 - main - INFO - 🧪 测试模式已启用
2025-07-30 12:58:49,849 - main - INFO - 🧪 测试模式：跳过GPIO初始化
2025-07-30 12:58:49,849 - main - INFO - 🧪 初始化模拟MQTT客户端...
2025-07-30 12:58:49,857 - main - INFO - 初始化传感器收集器...
2025-07-30 12:58:49,898 - main - INFO - 🧪 测试模式：跳过CO2控制器初始化
2025-07-30 12:58:49,899 - main - INFO - 🧪 测试模式：跳过设备自检系统初始化
2025-07-30 12:58:49,899 - main - INFO - 初始化蚊子检测文件监控...
2025-07-30 12:58:49,904 - main - INFO - 蚊子检测文件监控初始化完成
2025-07-30 12:58:49,907 - main - INFO - 内存监控已启动
2025-07-30 12:58:49,908 - main - INFO - 线程管理器已启动
2025-07-30 12:58:49,908 - main - INFO - 异步文件I/O已启动
2025-07-30 12:58:49,908 - main - INFO - 系统初始化完成
2025-07-30 12:58:49,908 - main - INFO - 
=== 启动传感器数据采集与上传系统 ===

2025-07-30 12:58:49,909 - main - INFO - 启动MQTT上传线程...
2025-07-30 12:58:49,909 - main - INFO - 🧪 测试模式：跳过MQTT连接等待，立即继续启动
2025-07-30 12:58:49,909 - main - INFO - 启动传感器收集线程...
2025-07-30 12:58:49,910 - main - INFO - 传感器收集线程已启动，数据采集间隔为60秒
2025-07-30 12:58:51,913 - main - INFO - 🧪 测试模式：跳过CO2控制器启动
2025-07-30 12:58:51,913 - main - INFO - 等待传感器数据采集完成第一个周期...
2025-07-30 12:58:56,918 - main - INFO - 🧪 测试模式：跳过设备自检系统启动
2025-07-30 12:58:56,918 - main - INFO - 启动蚊子检测文件监控...
2025-07-30 12:58:56,919 - main - INFO - 设备开机状态已在MQTT连接建立时自动上报，无需重复发送
2025-07-30 12:58:56,919 - main - INFO - 
系统已成功启动，按 Ctrl+C 停止
2025-07-30 13:03:57,430 - main - INFO - 系统运行正常，MQTT连接已建立
2025-07-30 13:08:57,731 - main - INFO - 系统运行正常，MQTT连接已建立
2025-07-30 13:13:33,069 - main - INFO - 
正在停止系统...
2025-07-30 13:13:33,070 - main - INFO - 上报设备关机状态...
2025-07-30 13:13:33,571 - main - INFO - 停止传感器收集线程...
2025-07-30 13:13:33,803 - main - INFO - 执行最终数据归档...
2025-07-30 13:13:33,803 - main - INFO - 最终数据归档完成
2025-07-30 13:13:33,803 - main - INFO - 停止MQTT客户端...
2025-07-30 13:13:33,812 - main - INFO - 停止蚊子检测文件监控...
2025-07-30 13:13:35,879 - main - INFO - 停止内存监控...
2025-07-30 13:13:40,882 - main - INFO - 停止异步文件I/O...
2025-07-30 13:13:41,706 - main - INFO - 停止线程管理器...
2025-07-30 13:13:50,718 - main - INFO - 所有组件已成功停止
2025-07-30 13:13:50,718 - main - INFO - 🧪 测试模式：跳过GPIO清理
2025-07-30 13:13:50,897 - main - INFO - 🧪 测试API服务器已停止
2025-07-30 13:13:50,897 - main - INFO - 系统已完全停止
