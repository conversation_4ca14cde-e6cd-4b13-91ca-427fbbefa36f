#!/bin/bash
echo "开始混合负载测试..."

# 同时进行传感器数据注入和蚊虫检测
{
  for i in {1..15}; do
    curl -X POST http://192.168.0.111:5001/api/data/inject/sensor \
         -H "Content-Type: application/json" \
         -d "{\"co2\": $((400 + RANDOM % 300)), \"temperature\": $((20 + RANDOM % 20)), \"humidity\": $((40 + RANDOM % 40))}" \
         -s -o /dev/null
    sleep 0.5
  done
} &

{
  for i in {1..3}; do
    curl -X POST http://192.168.0.111:5001/api/data/inject/mosquito \
         -H "Content-Type: application/json" \
         -d "{
           \"detection_id\": \"mixed_test_$(printf %03d $i)\",
           \"timestamp\": $((1722334800 + i * 120)),
           \"detections\": [
             {\"x\": $((RANDOM % 1000)), \"y\": $((RANDOM % 1000)), \"confidence\": 0.$((80 + RANDOM % 20))}
           ]
         }" \
         -s -o /dev/null
    sleep 2
  done
} &

wait
echo "混合负载测试完成"
