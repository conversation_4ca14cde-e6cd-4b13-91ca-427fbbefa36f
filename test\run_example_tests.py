#!/usr/bin/env python3
"""
测试运行示例脚本

演示如何运行不同类型的测试，适合快速验证测试框架功能。
"""

import subprocess
import sys
import time
from pathlib import Path


def run_command(cmd, description):
    """运行命令并显示结果"""
    print(f"\n{'='*60}")
    print(f"运行: {description}")
    print(f"命令: {' '.join(cmd)}")
    print('='*60)
    
    start_time = time.time()
    result = subprocess.run(cmd, capture_output=True, text=True)
    end_time = time.time()
    
    print(f"执行时间: {end_time - start_time:.2f}秒")
    print(f"返回码: {result.returncode}")
    
    if result.stdout:
        print("\n标准输出:")
        print(result.stdout)
    
    if result.stderr:
        print("\n错误输出:")
        print(result.stderr)
    
    return result.returncode == 0


def main():
    """主函数"""
    print("IoT传感器监控系统 - 测试框架演示")
    print("="*60)
    
    # 检查是否在正确的目录
    test_dir = Path(__file__).parent
    if not (test_dir / 'conftest.py').exists():
        print("错误: 请在test目录下运行此脚本")
        sys.exit(1)
    
    # 切换到test目录
    import os
    os.chdir(test_dir)
    
    success_count = 0
    total_tests = 0
    
    # 1. 运行基础单元测试
    total_tests += 1
    if run_command([
        sys.executable, "-m", "pytest", 
        "unit/test_sensors/test_sensor_collector.py", 
        "-v", "--tb=short"
    ], "基础传感器单元测试"):
        success_count += 1
    
    # 2. 运行MQTT客户端测试
    total_tests += 1
    if run_command([
        sys.executable, "-m", "pytest", 
        "unit/test_communication/test_mqtt_client.py", 
        "-v", "--tb=short"
    ], "MQTT客户端单元测试"):
        success_count += 1
    
    # 3. 运行设备健康检查测试
    total_tests += 1
    if run_command([
        sys.executable, "-m", "pytest", 
        "unit/test_utils/test_device_health_check.py", 
        "-v", "--tb=short"
    ], "设备健康检查单元测试"):
        success_count += 1
    
    # 4. 运行集成测试
    total_tests += 1
    if run_command([
        sys.executable, "-m", "pytest", 
        "integration/test_sensor_to_mqtt_flow.py", 
        "-v", "--tb=short"
    ], "传感器到MQTT数据流集成测试"):
        success_count += 1
    
    # 5. 运行API测试
    total_tests += 1
    if run_command([
        sys.executable, "-m", "pytest", 
        "api/test_http_api.py::TestHTTPAPI::test_health_endpoint", 
        "-v", "--tb=short"
    ], "HTTP API健康检查测试"):
        success_count += 1
    
    # 6. 运行MQTT接口测试
    total_tests += 1
    if run_command([
        sys.executable, "-m", "pytest", 
        "api/test_mqtt_interface.py::TestMQTTInterface::test_mqtt_client_connection", 
        "-v", "--tb=short"
    ], "MQTT接口连接测试"):
        success_count += 1
    
    # 7. 运行性能测试示例（快速版本）
    total_tests += 1
    if run_command([
        sys.executable, "-m", "pytest", 
        "performance/test_sensor_performance.py::TestSensorPerformance::test_sensor_data_collection_speed", 
        "-v", "--tb=short"
    ], "传感器数据收集性能测试"):
        success_count += 1
    
    # 8. 运行标记测试
    total_tests += 1
    if run_command([
        sys.executable, "-m", "pytest", 
        "-m", "unit and not slow", 
        "-v", "--tb=short", "--maxfail=3"
    ], "运行所有快速单元测试"):
        success_count += 1
    
    # 9. 生成简单的测试报告
    total_tests += 1
    if run_command([
        sys.executable, "-m", "pytest", 
        "unit/test_sensors/", 
        "--tb=short", "--quiet", 
        "--junit-xml=example_test_results.xml"
    ], "生成XML测试报告"):
        success_count += 1
    
    # 10. 运行覆盖率测试
    total_tests += 1
    if run_command([
        sys.executable, "-m", "pytest", 
        "unit/test_sensors/test_sensor_collector.py", 
        "--cov=fixtures", "--cov-report=term-missing", 
        "--tb=short"
    ], "运行覆盖率测试"):
        success_count += 1
    
    # 总结
    print(f"\n{'='*60}")
    print("测试运行总结")
    print('='*60)
    print(f"总测试组: {total_tests}")
    print(f"成功: {success_count}")
    print(f"失败: {total_tests - success_count}")
    print(f"成功率: {success_count/total_tests*100:.1f}%")
    
    if success_count == total_tests:
        print("\n🎉 所有测试组都运行成功！测试框架工作正常。")
        return 0
    else:
        print(f"\n⚠️  有 {total_tests - success_count} 个测试组失败。请检查上面的错误信息。")
        return 1


if __name__ == "__main__":
    sys.exit(main())
