#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import json
import time
import threading
import shutil
from datetime import datetime
import random
from utils.logger import get_logger
from utils.config_loader import load_config

class MosquitoFileMonitor:
    """蚊子检测文件监控系统，监控共享目录中的JSON文件并通过MQTT上传"""
    
    def __init__(self, config=None):
        """初始化监控器"""
        # 初始化日志
        self.logger = get_logger("mosquito_monitor")
        self.logger.info("初始化蚊子检测文件监控器...")

        # 加载配置
        self.config = config if config else load_config()
        self.mqtt_config = self.config['mqtt']
        self.mosquito_config = self.mqtt_config.get('mosquito_detection', {})
        
        # 设置路径 - 简化目录结构
        self.shared_data_path = self.mosquito_config.get('shared_data_path', "/home/<USER>/shared_data")
        self.incoming_dir = os.path.join(self.shared_data_path, "incoming")
        self.processed_dir = os.path.join(self.shared_data_path, "processed")
        
        # 确保目录存在
        self._ensure_directories()
        
        # 待处理文件队列
        self.pending_files = {}  # file_id -> {path, last_attempt, attempt_count, backoff_time}
        self.pending_lock = threading.Lock()
        
        # 运行状态
        self.running = False
        self.monitor_thread = None
        
        self.logger.info(f"蚊子检测文件监控器初始化完成，共享数据路径: {self.shared_data_path}")
    
    def _ensure_directories(self):
        """确保必要的目录存在"""
        for directory in [self.incoming_dir, self.processed_dir]:
            if not os.path.exists(directory):
                os.makedirs(directory, exist_ok=True)
                self.logger.info(f"创建目录: {directory}")
    
    def start(self):
        """启动监控线程"""
        if self.running:
            self.logger.warning("蚊子检测文件监控器已在运行中")
            return

        # 启动时清理待处理队列（防止重启后的状态不一致）
        with self.pending_lock:
            if self.pending_files:
                self.logger.info(f"启动时清理 {len(self.pending_files)} 个待处理文件记录")
                self.pending_files.clear()

        self.running = True
        self.monitor_thread = threading.Thread(target=self.monitor_loop, daemon=True)
        self.monitor_thread.start()
        self.logger.info("蚊子检测文件监控线程已启动")
    
    def stop(self):
        """停止监控线程"""
        self.running = False
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.logger.info("正在停止蚊子检测文件监控线程...")
            self.monitor_thread.join(timeout=5)
        self.logger.info("蚊子检测文件监控线程已停止")
    
    def monitor_loop(self):
        """监控incoming目录的新文件"""
        self.logger.info(f"开始监控目录: {self.incoming_dir}")
        
        # 获取检查间隔时间
        check_interval = self.mosquito_config.get('file_check_interval', 5)
        
        while self.running:
            try:
                # 检查目录中的所有文件
                if os.path.exists(self.incoming_dir):
                    for filename in os.listdir(self.incoming_dir):
                        # 跳过临时文件
                        if filename.endswith('.tmp'):
                            continue
                        
                        # 处理JSON文件
                        if filename.endswith('.json'):
                            file_path = os.path.join(self.incoming_dir, filename)

                            # 避免重复处理（使用锁保护）
                            with self.pending_lock:
                                should_process = filename not in self.pending_files

                            if should_process:
                                self.logger.info(f"发现新文件: {filename}")
                                self.process_file(file_path)
                
                # 检查待处理文件是否需要重试
                self.check_pending_files()
                
                # 等待下一次检查
                time.sleep(check_interval)
                
            except Exception as e:
                self.logger.error(f"监控循环发生错误: {e}", exc_info=True)
                time.sleep(check_interval)  # 发生错误后等待
    
    def read_mosquito_file(self, file_path):
        """读取蚊子检测文件并验证JSON有效性
        
        Args:
            file_path: 文件路径
            
        Returns:
            dict|None: 解析后的JSON数据，解析失败则返回None
        """
        try:
            with open(file_path, 'r') as f:
                data = json.load(f)
            return data
        except json.JSONDecodeError as e:
            self.logger.error(f"文件格式错误，不是有效JSON: {file_path}, 错误: {e}")
            return None
        except Exception as e:
            self.logger.error(f"读取文件失败: {file_path}, 错误: {e}")
            return None
    
    def process_file(self, file_path):
        """处理新的蚊子检测文件

        Args:
            file_path: 文件路径

        Returns:
            bool: 是否成功处理
        """
        try:
            file_id = os.path.basename(file_path)
            self.logger.info(f"开始处理文件: {file_id}")

            # 检查文件是否仍然存在（可能被其他进程移动）
            if not os.path.exists(file_path):
                self.logger.warning(f"文件不再存在，跳过处理: {file_path}")
                return False
                
            # 读取文件
            data = self.read_mosquito_file(file_path)
            if not data:
                self.logger.error(f"文件不是有效JSON: {file_id}")
                return False
            
            # 获取MQTT客户端
            import builtins
            if not hasattr(builtins, 'mqtt_client_instance'):
                self.logger.error("MQTT客户端实例不可用")
                return False
                
            mqtt_client = getattr(builtins, 'mqtt_client_instance')
            
            # 填充设备ID
            data["devid"] = mqtt_client.device_id
            
            # 记录文件处理状态
            with self.pending_lock:
                if file_id not in self.pending_files:
                    self.pending_files[file_id] = {
                        "path": file_path,
                        "last_attempt": time.time(),
                        "attempt_count": 0,
                        "backoff_time": 5  # 初始退避时间5秒
                    }
                else:
                    # 更新尝试信息
                    self.pending_files[file_id]["last_attempt"] = time.time()
                    self.pending_files[file_id]["attempt_count"] += 1

                    # 检查是否超过最大重试次数
                    max_retry_count = self.mosquito_config.get('max_retry_count', 3)
                    if self.pending_files[file_id]["attempt_count"] > max_retry_count:
                        self.logger.error(f"文件处理失败，超过最大重试次数({max_retry_count}): {file_id}")
                        # 移动到failed目录（如果存在的话）
                        failed_dir = os.path.join(self.shared_data_path, "failed")
                        if not os.path.exists(failed_dir):
                            os.makedirs(failed_dir, exist_ok=True)
                        try:
                            failed_path = os.path.join(failed_dir, file_id)
                            shutil.move(file_path, failed_path)
                            self.logger.info(f"文件已移动到失败目录: {failed_path}")
                        except Exception as e:
                            self.logger.error(f"移动文件到失败目录失败: {e}")
                        # 从待处理列表中移除
                        del self.pending_files[file_id]
                        return False

                    # 指数级增加退避时间，但设置上限为5分钟
                    self.pending_files[file_id]["backoff_time"] = min(
                        300,
                        5 * (2 ** self.pending_files[file_id]["attempt_count"])
                    )
                    
            # 在发送之前先记录日志，避免日志顺序混淆
            self.logger.info(f"蚊子检测数据已提交处理: {file_id}")

            # 发布数据
            try:
                # 直接发布数据，不使用file_id
                success = mqtt_client.publish_mosquito_data_simple(data)

                # 根据发布结果处理文件
                if success:
                    # 发布成功，移动文件到processed目录
                    move_result = self.move_to_processed(file_path)
                    if move_result:
                        # 从待处理列表中移除
                        with self.pending_lock:
                            if file_id in self.pending_files:
                                del self.pending_files[file_id]
                        self.logger.info(f"文件处理成功并已移动: {file_id}")
                    else:
                        self.logger.warning(f"文件发布成功但移动失败: {file_id}")
                        success = False
                else:
                    self.logger.warning(f"MQTT发布失败: {file_id}")

            except Exception as e:
                self.logger.error(f"MQTT发布调用异常: {file_id}, 错误: {e}", exc_info=True)
                success = False

            return success
            
        except Exception as e:
            self.logger.error(f"处理文件时发生错误: {e}", exc_info=True)
            return False
    
    def move_to_processed(self, file_path):
        """将文件移至已处理目录

        Args:
            file_path: 原文件路径

        Returns:
            bool: 是否成功移动
        """
        try:
            if not os.path.exists(file_path):
                self.logger.warning(f"要移动的文件不存在: {file_path}")
                return False

            filename = os.path.basename(file_path)
            target_path = os.path.join(self.processed_dir, filename)  # 保持原文件名不变

            # 检查目标目录是否存在
            if not os.path.exists(self.processed_dir):
                os.makedirs(self.processed_dir, exist_ok=True)

            # 如果目标文件已存在，添加后缀避免冲突
            if os.path.exists(target_path):
                name, ext = os.path.splitext(filename)
                target_path = os.path.join(self.processed_dir, f"{name}_{int(time.time())}{ext}")

            # 直接移动文件
            shutil.move(file_path, target_path)

            self.logger.info(f"文件已处理并移至: {target_path}")
            return True
        except Exception as e:
            self.logger.error(f"移动文件到已处理目录失败: {e}", exc_info=True)
            return False
    
    def check_pending_files(self):
        """检查待处理文件并应用指数级退避重试"""
        try:
            now = time.time()
            
            # 收集需要重试的文件信息
            files_to_retry = []

            with self.pending_lock:
                for file_id, info in list(self.pending_files.items()):
                    file_path = info["path"]
                    
                    # 检查是否到了重试时间
                    elapsed = now - info["last_attempt"]
                    if elapsed > info["backoff_time"]:
                        if os.path.exists(file_path):
                            files_to_retry.append((file_id, file_path))
                        else:
                            # 文件不存在，从列表中删除
                            del self.pending_files[file_id]

            # 在锁外重新处理文件
            for file_id, file_path in files_to_retry:
                self.logger.info(f"重试处理文件: {file_id}, 第{self.pending_files[file_id]['attempt_count'] + 1}次")
                # 重新处理文件
                self.process_file(file_path)
                   
        except Exception as e:
            self.logger.error(f"检查待处理文件时发生错误: {e}", exc_info=True)
    
    def handle_result_simple(self, success, data):
        """处理MQTT回复结果（基于devid匹配）
        
        Args:
            success: 是否成功
            data: 回复数据
        """
        with self.pending_lock:
            if not self.pending_files:
                self.logger.debug("收到蚊子检测回复，但没有待处理的文件")
                return

            # 收到成功回复后，将所有待处理文件移到processed目录
            if success:
                files_to_process = list(self.pending_files.items())
                
                for file_id, info in files_to_process:
                    file_path = info["path"]
                    
                    # 只处理存在的文件
                    if not os.path.exists(file_path):
                        del self.pending_files[file_id]
                        continue
                        
                    # 移动到processed目录
                    move_result = self.move_to_processed(file_path)
                    if move_result:
                        del self.pending_files[file_id]
                    # 如果移动失败，保留在待处理列表中