# 测试指南

## 🌐 网络配置重要说明

**⚠️ 重要**: 如果测试人员与主板不在同一内网，需要将所有示例中的 `主板IP` 替换为实际的主板IP地址。

### 快速配置步骤：
1. **获取主板IP**: 在主板上运行 `ipconfig` (Windows) 或 `ip addr` (Linux)
2. **替换IP地址**: 将示例中的 `主板IP` 替换为实际IP，如 `*************`
3. **配置防火墙**: 确保主板防火墙允许5001端口访问
4. **测试连通性**: 运行 `curl http://主板IP:5001/api/health` 验证连接

📖 **详细网络配置**: 请参考 [网络配置指南](network_setup.md)

## 🎯 测试目标

本测试系统旨在验证IoT传感器监控系统的以下功能：
- 传感器数据采集和处理
- MQTT通信功能
- 系统性能和稳定性
- 错误处理和恢复机制

## 🧪 测试类型

### 1. 功能测试

#### 1.1 传感器数据采集测试
**目标**: 验证各类传感器数据的正确采集和处理

**测试步骤**:
1. 启动测试环境
2. 开始测试会话
3. 注入各类传感器数据
4. 验证数据处理结果
5. 检查数据存储格式

**示例命令**:
```bash
# 1. 开始测试会话
curl -X POST http://主板IP:5001/api/test/session/start \
     -H "Content-Type: application/json" \
     -d '{"name": "传感器功能测试"}'

# 2. 注入CO2数据
curl -X POST http://主板IP:5001/api/data/inject/sensor \
     -H "Content-Type: application/json" \
     -d '{"sensor_type": "co2", "value": 450, "unit": "ppm"}'

# 3. 注入温度数据
curl -X POST http://主板IP:5001/api/data/inject/sensor \
     -H "Content-Type: application/json" \
     -d '{"sensor_type": "temp", "value": 25.5, "unit": "°C"}'

# 4. 检查处理结果
curl http://主板IP:5001/api/mock/mqtt/messages?type=sensor_data
```

#### 1.2 MQTT通信测试
**目标**: 验证MQTT消息发布和处理功能

**测试步骤**:
1. 检查MQTT客户端状态
2. 注入数据触发MQTT发布
3. 验证消息格式和内容
4. 测试网络故障恢复

#### 1.3 错误处理测试
**目标**: 验证系统的错误处理和恢复能力

**测试步骤**:
1. 注入无效数据
2. 模拟传感器故障
3. 模拟网络故障
4. 验证错误日志和恢复机制

### 2. 性能测试

#### 2.1 数据处理性能测试
**目标**: 测试系统的数据处理能力和响应时间

**测试步骤**:
1. 开始性能监控
2. 批量注入数据
3. 监控系统资源使用
4. 分析处理性能

**示例命令**:
```bash
# 1. 开始性能监控
curl -X POST http://主板IP:5001/api/monitor/start \
     -H "Content-Type: application/json" \
     -d '{"interval": 1, "duration": 300}'

# 2. 批量注入数据
curl -X POST http://主板IP:5001/api/data/inject/batch \
     -H "Content-Type: application/json" \
     -d '{
       "data_list": [
         {"sensor_type": "co2", "value": 450},
         {"sensor_type": "temp", "value": 25.5},
         {"sensor_type": "hum", "value": 60.0}
       ]
     }'

# 3. 获取性能数据
curl http://主板IP:5001/api/monitor/data?limit=10
```

#### 2.2 系统资源监控
**目标**: 监控系统资源使用情况

**监控指标**:
- CPU使用率
- 内存使用量
- 磁盘I/O
- 网络流量

### 3. 压力测试

#### 3.1 高频数据注入测试
**目标**: 测试系统在高频数据输入下的稳定性

**测试方法**:
- 使用脚本连续注入数据
- 监控系统响应时间
- 检查数据丢失情况

#### 3.2 长时间运行测试
**目标**: 验证系统长时间运行的稳定性

**测试方法**:
- 连续运行24小时以上
- 定期注入数据
- 监控内存泄漏
- 检查系统性能衰减

## 📊 测试场景

### 场景1: 正常工作日测试
模拟正常工作日的数据采集模式：
- 每分钟采集一次传感器数据
- 偶尔的网络波动
- 正常的系统重启

### 场景2: 异常环境测试
模拟异常环境下的系统行为：
- 传感器故障
- 网络中断
- 电源波动
- 极端数据值

### 场景3: 高负载测试
模拟高负载情况：
- 多传感器同时工作
- 高频数据采集
- 大量MQTT消息
- 系统资源紧张

## 🔍 测试验证

### 数据验证
1. **数据完整性**: 检查数据是否完整保存
2. **数据格式**: 验证数据格式符合规范
3. **数据一致性**: 确保处理前后数据一致

### 性能验证
1. **响应时间**: API响应时间 < 1秒
2. **吞吐量**: 每秒处理数据量 > 10条
3. **资源使用**: CPU < 80%, 内存 < 1GB

### 稳定性验证
1. **错误率**: 错误率 < 1%
2. **可用性**: 系统可用性 > 99%
3. **恢复时间**: 故障恢复时间 < 30秒

## 📝 测试报告

### 报告内容
1. **测试概述**: 测试目标、范围、环境
2. **测试结果**: 各项测试的执行结果
3. **性能数据**: 系统性能指标统计
4. **问题记录**: 发现的问题和解决方案
5. **建议**: 优化建议和改进方向

### 报告生成
```bash
# 获取测试会话状态（包含统计信息）
curl http://主板IP:5001/api/test/session/status

# 获取性能监控数据
curl http://主板IP:5001/api/monitor/data?format=csv

# 获取MQTT消息统计
curl http://主板IP:5001/api/mock/mqtt/messages?limit=1000
```

## ⚠️ 注意事项

1. **环境隔离**: 确保测试环境与生产环境完全隔离
2. **数据备份**: 测试前备份重要数据
3. **资源监控**: 测试过程中持续监控系统资源
4. **测试记录**: 详细记录测试过程和结果
5. **问题跟踪**: 及时记录和跟踪发现的问题

## 🔗 相关工具

- **Postman**: 用于API接口测试
- **curl**: 命令行HTTP客户端
- **JMeter**: 性能和压力测试工具
- **监控工具**: 系统资源监控工具
