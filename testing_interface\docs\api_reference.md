# API接口文档

## 📋 接口概览

测试API服务器提供了完整的HTTP接口，支持系统控制、数据注入、性能监控和测试管理。

**基础URL**: `http://localhost:8080`

## 🔧 系统控制接口

### 获取系统信息
```http
GET /api/info
```
**响应示例**:
```json
{
  "name": "IoT传感器监控系统测试API",
  "version": "1.0.0",
  "test_mode": true,
  "timestamp": "2025-07-30T13:21:00"
}
```

### 系统健康检查
```http
GET /api/health
```

### 获取系统状态
```http
GET /api/system/status
```

### 系统控制
```http
POST /api/system/start
POST /api/system/stop
POST /api/system/restart
```

### 电源控制
```http
POST /api/system/power
Content-Type: application/json

{
  "action": "on"  // "on" 或 "off"
}
```

### 组件状态查询
```http
GET /api/system/components
```

### 组件控制
```http
POST /api/system/components/{component_name}
Content-Type: application/json

{
  "action": "start"  // "start" 或 "stop"
}
```

### 传感器状态查询
```http
GET /api/system/sensors
```

### 传感器控制
```http
POST /api/system/sensors/{sensor_name}
Content-Type: application/json

{
  "action": "enable"  // "enable" 或 "disable"
}
```

## 📊 数据注入接口

### 注入传感器数据
```http
POST /api/data/inject/sensor
Content-Type: application/json

{
  "sensor_type": "co2",
  "value": 450,
  "unit": "ppm",
  "timestamp": "2025-07-30T13:21:00"
}
```

**支持的传感器类型**:
- `co2`: 二氧化碳浓度
- `temp`: 温度
- `hum`: 湿度
- `gps`: GPS定位
- `wind_speed`: 风速

### 注入错误数据
```http
POST /api/data/inject/error
Content-Type: application/json

{
  "error_type": "sensor_failure",
  "component": "co2_sensor",
  "message": "传感器读取超时",
  "severity": "error"
}
```

### 批量数据注入
```http
POST /api/data/inject/batch
Content-Type: application/json

{
  "data_list": [
    {
      "sensor_type": "co2",
      "value": 450,
      "unit": "ppm"
    },
    {
      "sensor_type": "temp",
      "value": 25.5,
      "unit": "°C"
    }
  ]
}
```

## 📈 性能监控接口

### 开始性能监控
```http
POST /api/monitor/start
Content-Type: application/json

{
  "interval": 5,  // 监控间隔(秒)
  "duration": 300  // 监控时长(秒)，可选
}
```

### 停止性能监控
```http
POST /api/monitor/stop
```

### 获取监控数据
```http
GET /api/monitor/data?limit=50&format=json
```

**查询参数**:
- `limit`: 返回数据条数限制
- `format`: 数据格式 (`json` 或 `csv`)

## 🧪 测试环境控制

### 获取测试环境状态
```http
GET /api/test/env/status
```

### 重置测试环境
```http
POST /api/test/env/reset
```

### 获取测试配置
```http
GET /api/test/env/config
```

### 更新测试配置
```http
POST /api/test/env/config
Content-Type: application/json

{
  "test_api": {
    "monitoring_interval": 10
  },
  "sensors": {
    "co2": {
      "enabled": true
    }
  }
}
```

## 🎯 测试会话管理

### 开始测试会话
```http
POST /api/test/session/start
Content-Type: application/json

{
  "name": "功能测试",
  "description": "传感器数据采集测试"
}
```

### 结束测试会话
```http
POST /api/test/session/stop
```

### 获取会话状态
```http
GET /api/test/session/status
```

## 🔍 模拟数据管理

### 获取MQTT消息
```http
GET /api/mock/mqtt/messages?limit=50&type=sensor_data
```

**查询参数**:
- `limit`: 消息数量限制
- `type`: 消息类型过滤

### 清空MQTT消息
```http
POST /api/mock/mqtt/clear
```

### 模拟网络故障
```http
POST /api/mock/network/failure
Content-Type: application/json

{
  "duration": 30.0,    // 故障持续时间(秒)
  "failure_rate": 1.0  // 失败率 (0.0-1.0)
}
```

## 📋 响应格式

### 成功响应
```json
{
  "message": "操作成功",
  "data": { ... },
  "timestamp": "2025-07-30T13:21:00"
}
```

### 错误响应
```json
{
  "error": "错误描述",
  "code": "ERROR_CODE",
  "timestamp": "2025-07-30T13:21:00"
}
```

## 🔒 错误代码

| 代码 | 描述 |
|------|------|
| 400 | 请求参数错误 |
| 404 | 接口不存在 |
| 500 | 服务器内部错误 |

## 📝 使用示例

详细的使用示例请参考 [examples.md](examples.md)。
