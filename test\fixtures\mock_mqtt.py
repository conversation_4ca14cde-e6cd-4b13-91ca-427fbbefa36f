"""
MQTT客户端模拟对象

提供MQTT客户端的Mock实现，用于测试时替代真实的MQTT连接。
"""

import time
import json
import queue
import threading
from unittest.mock import Mock, MagicMock
from typing import Dict, Any, Optional, Callable


class MockMQTTClient:
    """MQTT客户端模拟"""
    
    def __init__(self, device_id="TEST_DEVICE_001"):
        self.device_id = device_id
        self.connected = False
        self.running = False
        
        # 模拟队列
        self.sensor_data_queue = queue.Queue()
        self.error_data_queue = queue.Queue()
        self.co2_status_queue = queue.Queue()
        self.check_data_queue = queue.Queue()
        
        # 发布历史
        self.published_messages = []
        self.publish_success_rate = 1.0  # 发布成功率
        
        # 回调函数
        self.on_connect_callback = None
        self.on_disconnect_callback = None
        self.on_message_callback = None
        
        # 模拟延迟
        self.publish_delay = 0.0
        
    def connect(self) -> bool:
        """模拟连接"""
        self.connected = True
        if self.on_connect_callback:
            self.on_connect_callback(None, None, None, 0)
        return True
    
    def disconnect(self):
        """模拟断开连接"""
        self.connected = False
        if self.on_disconnect_callback:
            self.on_disconnect_callback(None, None, 1)
    
    def start(self):
        """启动客户端"""
        self.running = True
        self.connect()
    
    def stop(self):
        """停止客户端"""
        self.running = False
        self.disconnect()
    
    def publish_sensor_data(self, data: Dict[str, Any]) -> bool:
        """模拟发布传感器数据"""
        if self.publish_delay > 0:
            time.sleep(self.publish_delay)
            
        success = self._should_publish_succeed()
        
        if success:
            message = {
                "type": "sensor_data",
                "data": data,
                "timestamp": time.time(),
                "device_id": self.device_id
            }
            self.published_messages.append(message)
        
        return success
    
    def publish_error_data(self, error_data: Dict[str, Any]) -> bool:
        """模拟发布错误数据"""
        if self.publish_delay > 0:
            time.sleep(self.publish_delay)
            
        success = self._should_publish_succeed()
        
        if success:
            message = {
                "type": "error_data",
                "data": error_data,
                "timestamp": time.time(),
                "device_id": self.device_id
            }
            self.published_messages.append(message)
        
        return success
    
    def publish_co2_status(self, status_data: Dict[str, Any]) -> bool:
        """模拟发布CO2状态数据"""
        if self.publish_delay > 0:
            time.sleep(self.publish_delay)
            
        success = self._should_publish_succeed()
        
        if success:
            message = {
                "type": "co2_status",
                "data": status_data,
                "timestamp": time.time(),
                "device_id": self.device_id
            }
            self.published_messages.append(message)
        
        return success
    
    def publish_check_data(self, check_data: Dict[str, Any]) -> bool:
        """模拟发布设备检查数据"""
        if self.publish_delay > 0:
            time.sleep(self.publish_delay)
            
        success = self._should_publish_succeed()
        
        if success:
            message = {
                "type": "check_data",
                "data": check_data,
                "timestamp": time.time(),
                "device_id": self.device_id
            }
            self.published_messages.append(message)
        
        return success
    
    def publish_mosquito_data_simple(self, mosquito_data: Dict[str, Any]) -> bool:
        """模拟发布蚊子检测数据"""
        if self.publish_delay > 0:
            time.sleep(self.publish_delay)
            
        success = self._should_publish_succeed()
        
        if success:
            message = {
                "type": "mosquito_data",
                "data": mosquito_data,
                "timestamp": time.time(),
                "device_id": self.device_id
            }
            self.published_messages.append(message)
        
        return success
    
    def simulate_message_receive(self, topic: str, payload: str):
        """模拟接收消息"""
        if self.on_message_callback:
            mock_message = Mock()
            mock_message.topic = topic
            mock_message.payload = payload.encode() if isinstance(payload, str) else payload
            self.on_message_callback(None, None, mock_message)
    
    def set_publish_success_rate(self, rate: float):
        """设置发布成功率"""
        self.publish_success_rate = max(0.0, min(1.0, rate))
    
    def set_publish_delay(self, delay: float):
        """设置发布延迟"""
        self.publish_delay = max(0.0, delay)
    
    def get_published_messages(self, message_type: Optional[str] = None):
        """获取已发布的消息"""
        if message_type:
            return [msg for msg in self.published_messages if msg["type"] == message_type]
        return self.published_messages.copy()
    
    def clear_published_messages(self):
        """清空已发布的消息"""
        self.published_messages.clear()
    
    def _should_publish_succeed(self) -> bool:
        """判断发布是否应该成功"""
        import random
        return random.random() < self.publish_success_rate


def create_mock_mqtt_client(device_id="TEST_DEVICE_001", **kwargs):
    """创建MQTT客户端模拟对象的工厂函数"""
    client = MockMQTTClient(device_id)
    
    # 应用额外配置
    for key, value in kwargs.items():
        if hasattr(client, key):
            setattr(client, key, value)
    
    return client


class MockMQTTBroker:
    """MQTT代理模拟"""
    
    def __init__(self, host="localhost", port=1883):
        self.host = host
        self.port = port
        self.clients = {}
        self.messages = []
        self.running = False
        
    def start(self):
        """启动代理"""
        self.running = True
    
    def stop(self):
        """停止代理"""
        self.running = False
        self.clients.clear()
    
    def register_client(self, client_id: str, client: MockMQTTClient):
        """注册客户端"""
        self.clients[client_id] = client
    
    def unregister_client(self, client_id: str):
        """注销客户端"""
        self.clients.pop(client_id, None)
    
    def publish_message(self, topic: str, payload: str, sender_id: str = None):
        """发布消息到所有订阅的客户端"""
        message = {
            "topic": topic,
            "payload": payload,
            "sender_id": sender_id,
            "timestamp": time.time()
        }
        self.messages.append(message)
        
        # 通知所有客户端
        for client_id, client in self.clients.items():
            if client_id != sender_id:  # 不发送给发送者
                client.simulate_message_receive(topic, payload)
    
    def get_messages(self, topic_filter: Optional[str] = None):
        """获取消息历史"""
        if topic_filter:
            return [msg for msg in self.messages if topic_filter in msg["topic"]]
        return self.messages.copy()
    
    def clear_messages(self):
        """清空消息历史"""
        self.messages.clear()
