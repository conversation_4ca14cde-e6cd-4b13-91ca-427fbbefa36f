# 🚀 快速测试指南

## 📋 测试前准备

### 1. 启动测试环境
```bash
# 在项目根目录执行
python main.py --test-mode --config testing_interface/config/config_test.yaml --test-api --test-port 5001
```

### 2. 获取主板IP地址
```bash
# Windows
ipconfig

# Linux/Mac
ip addr show
# 或
ifconfig
```

### 3. 设置环境变量 (可选)
```bash
# Windows
set BOARD_IP=*************

# Linux/Mac
export BOARD_IP=*************
```

## 🧪 快速测试方法

### 方法1: 使用自动化脚本 (推荐)

#### 基础功能测试 (2-3分钟)
```bash
# Windows
bash testing_interface/scripts/run_basic_tests.sh

# 或直接运行
testing_interface/scripts/run_basic_tests.sh
```

#### 综合功能测试 (5-10分钟)
```bash
bash testing_interface/scripts/run_comprehensive_tests.sh
```

#### 性能压力测试 (10-15分钟)
```bash
bash testing_interface/scripts/run_performance_tests.sh
```

### 方法2: 手动测试核心功能

#### 1. 基础连接测试
```bash
# 替换IP为实际主板IP
curl http://*************:5001/api/health
curl http://*************:5001/api/info
curl http://*************:5001/api/system/status
```

#### 2. 数据注入测试
```bash
# CO2数据注入
curl -X POST http://*************:5001/api/data/inject/sensor \
     -H "Content-Type: application/json" \
     -d '{"sensor_type": "co2", "value": 450, "unit": "ppm"}'

# 温度数据注入
curl -X POST http://*************:5001/api/data/inject/sensor \
     -H "Content-Type: application/json" \
     -d '{"sensor_type": "temperature", "value": 25.5, "unit": "°C"}'

# 批量数据注入
curl -X POST http://*************:5001/api/data/inject/batch \
     -H "Content-Type: application/json" \
     -d '{
       "batch_id": "test_001",
       "data": [
         {"sensor_type": "co2", "value": 420},
         {"sensor_type": "temperature", "value": 24.8},
         {"sensor_type": "humidity", "value": 65.2}
       ]
     }'
```

#### 3. 性能监控测试
```bash
# 启动监控
curl -X POST http://*************:5001/api/monitor/start \
     -H "Content-Type: application/json" \
     -d '{"interval": 2, "duration": 30}'

# 等待几秒后获取数据
sleep 5
curl http://*************:5001/api/monitor/data?limit=5

# 停止监控
curl -X POST http://*************:5001/api/monitor/stop
```

#### 4. 测试会话管理
```bash
# 开始测试会话
curl -X POST http://*************:5001/api/test/session/start \
     -H "Content-Type: application/json" \
     -d '{"name": "手动测试会话", "test_type": "manual"}'

# 获取会话状态
curl http://*************:5001/api/test/session/status

# 结束会话
curl -X POST http://*************:5001/api/test/session/stop
```

#### 5. MQTT模拟验证
```bash
# 查看MQTT消息
curl http://*************:5001/api/mock/mqtt/messages?limit=10

# 模拟网络故障
curl -X POST http://*************:5001/api/mock/network/failure \
     -H "Content-Type: application/json" \
     -d '{"duration": 10, "failure_rate": 0.5}'

# 清空消息历史
curl -X POST http://*************:5001/api/mock/mqtt/clear
```

## 🌐 浏览器测试

### 访问API文档
```
http://*************:5001/
```

### 查看系统信息
```
http://*************:5001/api/info
```

### 获取系统状态
```
http://*************:5001/api/system/status
```

## 📊 验证测试结果

### 1. 检查数据文件
```bash
# 查看传感器数据
ls -la test_data/sensors/current/
cat test_data/sensors/current/sensor_data_$(date +%Y%m%d).json | tail -5

# 查看设备状态
cat test_data/devices/current/device_status.json

# 查看错误日志
cat test_data/logs/error_$(date +%Y%m%d).log
```

### 2. 验证JSON格式
```bash
# 验证数据格式正确性
python -m json.tool test_data/sensors/current/sensor_data_$(date +%Y%m%d).json
```

### 3. 检查系统日志
```bash
# 查看系统运行日志
tail -f logs/system.log
```

## ⚠️ 常见问题排查

### 连接问题
```bash
# 检查服务是否启动
curl http://localhost:5001/api/health

# 检查端口是否监听
netstat -an | findstr 5001  # Windows
netstat -an | grep 5001     # Linux

# 检查防火墙设置
# Windows: 控制面板 -> 系统和安全 -> Windows Defender 防火墙
# Linux: sudo ufw status
```

### 数据问题
```bash
# 检查test_data目录权限
ls -la test_data/

# 检查磁盘空间
df -h  # Linux
dir test_data  # Windows
```

### 性能问题
```bash
# 检查系统资源
curl http://*************:5001/api/monitor/data?limit=1

# 查看进程状态
ps aux | grep python  # Linux
tasklist | findstr python  # Windows
```

## 📈 测试成功标准

### ✅ 基础功能测试通过标准
- [ ] 所有API接口返回200状态码
- [ ] 数据注入后在test_data目录中生成对应文件
- [ ] MQTT模拟消息正确记录
- [ ] 性能监控数据完整
- [ ] 测试会话正常创建和结束

### ✅ 性能测试通过标准
- [ ] API响应时间 < 1秒
- [ ] 并发请求成功率 > 95%
- [ ] 系统资源使用率 < 80%
- [ ] 无内存泄漏现象
- [ ] 长时间运行稳定

### ✅ 数据一致性验证
- [ ] 注入的数据与存储的数据一致
- [ ] JSON格式正确
- [ ] 时间戳格式正确
- [ ] 数据类型验证通过

## 🎯 推荐测试流程

### 第一次测试 (完整验证)
1. 运行基础功能测试: `bash testing_interface/scripts/run_basic_tests.sh`
2. 运行综合功能测试: `bash testing_interface/scripts/run_comprehensive_tests.sh`
3. 检查测试报告: `cat test_data/reports/comprehensive_test_*.log`
4. 验证数据文件: 检查test_data目录内容

### 日常测试 (快速验证)
1. 运行基础功能测试: `bash testing_interface/scripts/run_basic_tests.sh`
2. 手动测试关键功能: 数据注入 + 监控
3. 检查系统状态: `curl http://IP:5001/api/system/status`

### 性能测试 (定期执行)
1. 运行性能测试: `bash testing_interface/scripts/run_performance_tests.sh`
2. 分析性能数据
3. 记录性能基线

---

**测试完成后记得**:
1. 停止测试服务: `Ctrl+C`
2. 查看测试报告
3. 备份重要测试数据 (可选)
4. 清理临时文件 (可选): `rm -rf test_data/*`
