"""
传感器系统性能测试

测试传感器数据收集、处理和传输的性能指标。
"""

import pytest
import time
import threading
from unittest.mock import Mock, patch
from test.fixtures.mock_sensors import create_mock_sensor_collector
from test.fixtures.mock_mqtt import MockMQTTClient
from test.fixtures.test_data import TestDataGenerator
from test.fixtures.config_samples import get_test_config


@pytest.mark.performance
class TestSensorPerformance:
    """传感器系统性能测试类"""
    
    def setup_method(self):
        """测试前设置"""
        self.config = get_test_config("performance")
        self.mock_logger = Mock()
        
    def test_sensor_data_collection_speed(self, benchmark):
        """测试传感器数据收集速度"""
        # 创建Mock传感器收集器
        sensor_collector = create_mock_sensor_collector(self.config)
        test_data = TestDataGenerator.generate_sensor_data()
        sensor_collector.collect_data.return_value = test_data
        
        # 基准测试数据收集
        result = benchmark(sensor_collector.collect_data)
        
        assert result is not None
        assert 'timestamp' in result
        
        # 验证性能要求（数据收集应该在100ms内完成）
        assert benchmark.stats['mean'] < 0.1  # 100ms
    
    def test_mqtt_publish_speed(self, benchmark):
        """测试MQTT发布速度"""
        # 创建Mock MQTT客户端
        mqtt_client = MockMQTTClient(self.config['mqtt']['device_id'])
        mqtt_client.connect()
        
        # 准备测试数据
        test_data = TestDataGenerator.generate_sensor_data()
        
        # 基准测试MQTT发布
        result = benchmark(mqtt_client.publish_sensor_data, test_data)
        
        assert result is True
        
        # 验证性能要求（MQTT发布应该在50ms内完成）
        assert benchmark.stats['mean'] < 0.05  # 50ms
    
    def test_batch_data_processing_performance(self, benchmark):
        """测试批量数据处理性能"""
        # 创建Mock组件
        sensor_collector = create_mock_sensor_collector(self.config)
        mqtt_client = MockMQTTClient(self.config['mqtt']['device_id'])
        mqtt_client.connect()
        
        # 准备批量测试数据
        batch_data = TestDataGenerator.generate_batch_sensor_data(100)
        
        def process_batch():
            """处理批量数据"""
            success_count = 0
            for data in batch_data:
                sensor_collector.collect_data.return_value = data
                collected_data = sensor_collector.collect_data()
                
                if collected_data:
                    result = mqtt_client.publish_sensor_data(collected_data)
                    if result:
                        success_count += 1
            return success_count
        
        # 基准测试批量处理
        result = benchmark(process_batch)
        
        assert result == 100
        
        # 验证性能要求（100条数据处理应该在5秒内完成）
        assert benchmark.stats['mean'] < 5.0  # 5秒
    
    def test_concurrent_data_collection_performance(self, benchmark):
        """测试并发数据收集性能"""
        def concurrent_collection():
            """并发数据收集"""
            collectors = []
            mqtt_clients = []
            
            # 创建多个收集器和客户端
            for i in range(5):
                collector = create_mock_sensor_collector(self.config)
                mqtt_client = MockMQTTClient(f"TEST_DEVICE_{i:03d}")
                mqtt_client.connect()
                
                collectors.append(collector)
                mqtt_clients.append(mqtt_client)
            
            def collect_worker(collector, mqtt_client):
                """工作线程"""
                for _ in range(10):
                    test_data = TestDataGenerator.generate_sensor_data()
                    collector.collect_data.return_value = test_data
                    
                    collected_data = collector.collect_data()
                    if collected_data:
                        mqtt_client.publish_sensor_data(collected_data)
            
            # 启动并发线程
            threads = []
            for collector, mqtt_client in zip(collectors, mqtt_clients):
                thread = threading.Thread(
                    target=collect_worker,
                    args=(collector, mqtt_client)
                )
                threads.append(thread)
                thread.start()
            
            # 等待所有线程完成
            for thread in threads:
                thread.join()
            
            # 统计总发布数量
            total_published = 0
            for mqtt_client in mqtt_clients:
                messages = mqtt_client.get_published_messages("sensor_data")
                total_published += len(messages)
            
            return total_published
        
        # 基准测试并发收集
        result = benchmark(concurrent_collection)
        
        assert result == 50  # 5个设备 × 10条消息
        
        # 验证性能要求（并发处理应该在3秒内完成）
        assert benchmark.stats['mean'] < 3.0  # 3秒
    
    def test_memory_usage_during_data_processing(self):
        """测试数据处理过程中的内存使用"""
        import psutil
        import os
        
        # 获取当前进程
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # 创建Mock组件
        sensor_collector = create_mock_sensor_collector(self.config)
        mqtt_client = MockMQTTClient(self.config['mqtt']['device_id'])
        mqtt_client.connect()
        
        # 处理大量数据
        for _ in range(1000):
            test_data = TestDataGenerator.generate_sensor_data()
            sensor_collector.collect_data.return_value = test_data
            
            collected_data = sensor_collector.collect_data()
            if collected_data:
                mqtt_client.publish_sensor_data(collected_data)
        
        # 检查内存使用
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory
        
        # 验证内存使用（增长不应超过50MB）
        assert memory_increase < 50, f"内存增长过多: {memory_increase:.2f}MB"
    
    def test_data_throughput(self):
        """测试数据吞吐量"""
        # 创建Mock组件
        sensor_collector = create_mock_sensor_collector(self.config)
        mqtt_client = MockMQTTClient(self.config['mqtt']['device_id'])
        mqtt_client.connect()
        
        # 测试持续时间
        test_duration = 10  # 10秒
        start_time = time.time()
        processed_count = 0
        
        # 持续处理数据
        while time.time() - start_time < test_duration:
            test_data = TestDataGenerator.generate_sensor_data()
            sensor_collector.collect_data.return_value = test_data
            
            collected_data = sensor_collector.collect_data()
            if collected_data:
                result = mqtt_client.publish_sensor_data(collected_data)
                if result:
                    processed_count += 1
        
        # 计算吞吐量
        actual_duration = time.time() - start_time
        throughput = processed_count / actual_duration
        
        # 验证吞吐量要求（至少每秒处理50条数据）
        assert throughput >= 50, f"吞吐量不足: {throughput:.2f} 条/秒"
    
    def test_latency_under_load(self):
        """测试负载下的延迟"""
        # 创建Mock组件
        sensor_collector = create_mock_sensor_collector(self.config)
        mqtt_client = MockMQTTClient(self.config['mqtt']['device_id'])
        mqtt_client.connect()
        
        # 设置轻微延迟模拟网络条件
        mqtt_client.set_publish_delay(0.001)  # 1ms延迟
        
        latencies = []
        
        # 测试100次数据处理的延迟
        for _ in range(100):
            test_data = TestDataGenerator.generate_sensor_data()
            sensor_collector.collect_data.return_value = test_data
            
            start_time = time.time()
            
            collected_data = sensor_collector.collect_data()
            if collected_data:
                mqtt_client.publish_sensor_data(collected_data)
            
            end_time = time.time()
            latency = (end_time - start_time) * 1000  # 转换为毫秒
            latencies.append(latency)
        
        # 计算延迟统计
        avg_latency = sum(latencies) / len(latencies)
        max_latency = max(latencies)
        
        # 验证延迟要求
        assert avg_latency < 10, f"平均延迟过高: {avg_latency:.2f}ms"
        assert max_latency < 50, f"最大延迟过高: {max_latency:.2f}ms"
    
    def test_cpu_usage_during_processing(self):
        """测试处理过程中的CPU使用率"""
        import psutil
        
        # 创建Mock组件
        sensor_collector = create_mock_sensor_collector(self.config)
        mqtt_client = MockMQTTClient(self.config['mqtt']['device_id'])
        mqtt_client.connect()
        
        # 开始监控CPU使用率
        cpu_samples = []
        
        def monitor_cpu():
            """监控CPU使用率"""
            for _ in range(20):  # 监控2秒（每100ms采样一次）
                cpu_percent = psutil.cpu_percent(interval=0.1)
                cpu_samples.append(cpu_percent)
        
        # 启动CPU监控线程
        monitor_thread = threading.Thread(target=monitor_cpu)
        monitor_thread.start()
        
        # 执行数据处理任务
        for _ in range(200):
            test_data = TestDataGenerator.generate_sensor_data()
            sensor_collector.collect_data.return_value = test_data
            
            collected_data = sensor_collector.collect_data()
            if collected_data:
                mqtt_client.publish_sensor_data(collected_data)
        
        # 等待监控完成
        monitor_thread.join()
        
        # 分析CPU使用率
        if cpu_samples:
            avg_cpu = sum(cpu_samples) / len(cpu_samples)
            max_cpu = max(cpu_samples)
            
            # 验证CPU使用率（平均不超过50%，峰值不超过80%）
            assert avg_cpu < 50, f"平均CPU使用率过高: {avg_cpu:.2f}%"
            assert max_cpu < 80, f"峰值CPU使用率过高: {max_cpu:.2f}%"
