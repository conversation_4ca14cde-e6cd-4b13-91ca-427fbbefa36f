# 🧪 综合测试计划

## 📋 测试概述

本文档提供完整的测试步骤，确保IoT传感器监控系统的所有功能正常工作。

## 🚀 测试前准备

### 1. 环境检查
```bash
# 检查Python环境
python --version

# 检查必要的包
pip list | grep -E "(flask|paho-mqtt|psutil|pyyaml)"

# 检查目录结构
ls -la testing_interface/
ls -la test_data/
```

### 2. 获取主板IP地址
```bash
# Windows
ipconfig

# Linux
ip addr show
# 或
ifconfig
```

### 3. 设置测试变量
```bash
# 设置主板IP (替换为实际IP)
export BOARD_IP="*************"  # Linux/Mac
set BOARD_IP=*************       # Windows

# 或者直接在命令中替换
```

## 🔧 阶段1: 基础功能测试

### 1.1 启动测试环境
```bash
# 启动测试模式
python main.py --test-mode --config testing_interface/config/config_test.yaml --test-api --test-port 5001

# 预期输出：
# ✅ 测试模式已启用
# ✅ 模拟MQTT客户端初始化成功
# ✅ 模拟硬件控制器初始化成功
# ✅ 测试API服务器已启动: http://0.0.0.0:5001
```

### 1.2 基础连接测试
```bash
# 健康检查
curl http://$BOARD_IP:5001/api/health

# 预期响应：
# {
#   "status": "healthy",
#   "timestamp": "2025-07-30T10:00:00",
#   "test_mode": true
# }

# 系统信息
curl http://$BOARD_IP:5001/api/info

# 预期响应：包含系统版本、测试模式状态等信息
```

### 1.3 API文档访问
```bash
# 浏览器访问API文档
# 打开浏览器访问: http://$BOARD_IP:5001/

# 或使用curl获取HTML文档
curl http://$BOARD_IP:5001/ > api_docs.html
```

## 🎛️ 阶段2: 系统控制功能测试

### 2.1 系统状态查询
```bash
# 获取系统状态
curl http://$BOARD_IP:5001/api/system/status

# 预期响应：
# {
#   "system_running": true,
#   "test_mode": true,
#   "components": {...},
#   "uptime": "..."
# }
```

### 2.2 组件状态查询
```bash
# 获取所有组件状态
curl http://$BOARD_IP:5001/api/system/components

# 获取传感器状态
curl http://$BOARD_IP:5001/api/system/sensors

# 预期响应：包含各组件的运行状态
```

### 2.3 系统控制操作
```bash
# 停止系统
curl -X POST http://$BOARD_IP:5001/api/system/stop

# 启动系统
curl -X POST http://$BOARD_IP:5001/api/system/start

# 重启系统
curl -X POST http://$BOARD_IP:5001/api/system/restart

# 每个操作后检查状态
curl http://$BOARD_IP:5001/api/system/status
```

### 2.4 组件控制测试
```bash
# 控制风扇
curl -X POST http://$BOARD_IP:5001/api/system/components/fan \
     -H "Content-Type: application/json" \
     -d '{"action": "start"}'

# 控制水泵
curl -X POST http://$BOARD_IP:5001/api/system/components/pump \
     -H "Content-Type: application/json" \
     -d '{"action": "stop"}'

# 控制传感器
curl -X POST http://$BOARD_IP:5001/api/system/sensors/co2 \
     -H "Content-Type: application/json" \
     -d '{"action": "enable"}'
```

## 📊 阶段3: 数据注入功能测试

### 3.1 单个传感器数据注入
```bash
# 注入CO2数据
curl -X POST http://$BOARD_IP:5001/api/data/inject/sensor \
     -H "Content-Type: application/json" \
     -d '{
       "sensor_type": "co2",
       "value": 450,
       "unit": "ppm",
       "timestamp": "'$(date -Iseconds)'"
     }'

# 注入温度数据
curl -X POST http://$BOARD_IP:5001/api/data/inject/sensor \
     -H "Content-Type: application/json" \
     -d '{
       "sensor_type": "temperature",
       "value": 25.5,
       "unit": "°C"
     }'

# 注入湿度数据
curl -X POST http://$BOARD_IP:5001/api/data/inject/sensor \
     -H "Content-Type: application/json" \
     -d '{
       "sensor_type": "humidity",
       "value": 65.2,
       "unit": "%"
     }'

# 注入GPS数据
curl -X POST http://$BOARD_IP:5001/api/data/inject/sensor \
     -H "Content-Type: application/json" \
     -d '{
       "sensor_type": "gps",
       "latitude": 39.9042,
       "longitude": 116.4074,
       "altitude": 43.5
     }'

# 注入风速数据
curl -X POST http://$BOARD_IP:5001/api/data/inject/sensor \
     -H "Content-Type: application/json" \
     -d '{
       "sensor_type": "wind_speed",
       "value": 3.2,
       "unit": "m/s",
       "direction": 180
     }'
```

### 3.2 错误数据注入测试
```bash
# 注入传感器错误
curl -X POST http://$BOARD_IP:5001/api/data/inject/error \
     -H "Content-Type: application/json" \
     -d '{
       "error_type": "sensor_error",
       "sensor_name": "co2_sensor",
       "error_message": "传感器读取超时",
       "error_code": "TIMEOUT_001"
     }'

# 注入通信错误
curl -X POST http://$BOARD_IP:5001/api/data/inject/error \
     -H "Content-Type: application/json" \
     -d '{
       "error_type": "communication_error",
       "component": "mqtt_client",
       "error_message": "MQTT连接失败",
       "error_code": "MQTT_CONN_FAIL"
     }'
```

### 3.3 批量数据注入测试
```bash
# 批量注入多种传感器数据
curl -X POST http://$BOARD_IP:5001/api/data/inject/batch \
     -H "Content-Type: application/json" \
     -d '{
       "batch_id": "test_batch_001",
       "data": [
         {
           "sensor_type": "co2",
           "value": 420,
           "unit": "ppm"
         },
         {
           "sensor_type": "temperature",
           "value": 24.8,
           "unit": "°C"
         },
         {
           "sensor_type": "humidity",
           "value": 68.5,
           "unit": "%"
         }
       ]
     }'
```

## 📈 阶段4: 性能监控功能测试

### 4.1 启动性能监控
```bash
# 开始性能监控
curl -X POST http://$BOARD_IP:5001/api/monitor/start \
     -H "Content-Type: application/json" \
     -d '{
       "interval": 2,
       "duration": 60,
       "metrics": ["cpu", "memory", "disk", "network"]
     }'

# 预期响应：
# {
#   "message": "性能监控已启动",
#   "monitor_id": "...",
#   "interval": 2,
#   "duration": 60
# }
```

### 4.2 获取监控数据
```bash
# 等待几秒后获取监控数据
sleep 5

# 获取最新监控数据
curl http://$BOARD_IP:5001/api/monitor/data?limit=5

# 获取特定指标
curl http://$BOARD_IP:5001/api/monitor/data?metrics=cpu,memory&limit=10

# 获取CSV格式数据
curl http://$BOARD_IP:5001/api/monitor/data?format=csv&limit=20
```

### 4.3 停止性能监控
```bash
# 停止监控
curl -X POST http://$BOARD_IP:5001/api/monitor/stop

# 预期响应：
# {
#   "message": "性能监控已停止",
#   "total_samples": 30,
#   "duration": "60.5s"
# }
```

## 🔧 阶段5: 模拟数据管理测试

### 5.1 MQTT消息查看
```bash
# 获取所有MQTT消息
curl http://$BOARD_IP:5001/api/mock/mqtt/messages

# 获取特定类型的消息
curl http://$BOARD_IP:5001/api/mock/mqtt/messages?type=sensor_data&limit=10

# 获取最近的消息
curl http://$BOARD_IP:5001/api/mock/mqtt/messages?limit=5&order=desc
```

### 5.2 网络故障模拟
```bash
# 模拟网络故障
curl -X POST http://$BOARD_IP:5001/api/mock/network/failure \
     -H "Content-Type: application/json" \
     -d '{
       "duration": 30,
       "failure_rate": 0.8,
       "failure_type": "timeout"
     }'

# 在故障期间尝试注入数据
curl -X POST http://$BOARD_IP:5001/api/data/inject/sensor \
     -H "Content-Type: application/json" \
     -d '{
       "sensor_type": "co2",
       "value": 500,
       "unit": "ppm"
     }'

# 检查MQTT消息状态
curl http://$BOARD_IP:5001/api/mock/mqtt/messages?limit=5
```

### 5.3 清理MQTT消息历史
```bash
# 清空消息历史
curl -X POST http://$BOARD_IP:5001/api/mock/mqtt/clear

# 验证清理结果
curl http://$BOARD_IP:5001/api/mock/mqtt/messages
```

## 🧪 阶段6: 测试会话管理

### 6.1 创建测试会话
```bash
# 开始新的测试会话
curl -X POST http://$BOARD_IP:5001/api/test/session/start \
     -H "Content-Type: application/json" \
     -d '{
       "name": "综合功能测试",
       "description": "测试所有系统功能",
       "test_type": "comprehensive",
       "duration": 300
     }'

# 预期响应：
# {
#   "session_id": "test_session_...",
#   "name": "综合功能测试",
#   "status": "active",
#   "start_time": "..."
# }
```

### 6.2 会话期间操作
```bash
# 获取会话状态
curl http://$BOARD_IP:5001/api/test/session/status

# 在会话中注入数据
curl -X POST http://$BOARD_IP:5001/api/data/inject/sensor \
     -H "Content-Type: application/json" \
     -d '{
       "sensor_type": "co2",
       "value": 480,
       "unit": "ppm"
     }'

# 启动性能监控
curl -X POST http://$BOARD_IP:5001/api/monitor/start \
     -H "Content-Type: application/json" \
     -d '{"interval": 1, "duration": 30}'
```

### 6.3 结束测试会话
```bash
# 结束测试会话
curl -X POST http://$BOARD_IP:5001/api/test/session/stop

# 预期响应：包含会话统计信息
# {
#   "session_id": "...",
#   "status": "completed",
#   "duration": "...",
#   "statistics": {
#     "total_requests": 15,
#     "data_injections": 8,
#     "errors": 0
#   }
# }
```

## 🌍 阶段7: 测试环境管理

### 7.1 环境状态检查
```bash
# 获取测试环境状态
curl http://$BOARD_IP:5001/api/test/env/status

# 预期响应：
# {
#   "test_mode": true,
#   "mock_mqtt": true,
#   "mock_hardware": true,
#   "data_isolation": true,
#   "test_data_dir": "test_data",
#   "session_active": false
# }
```

### 7.2 配置管理
```bash
# 获取当前测试配置
curl http://$BOARD_IP:5001/api/test/env/config

# 更新测试配置（谨慎使用）
curl -X POST http://$BOARD_IP:5001/api/test/env/config \
     -H "Content-Type: application/json" \
     -d '{
       "test_api": {
         "debug": false
       },
       "sensors": {
         "co2": {
           "mock_enabled": true
         }
       }
     }'
```

### 7.3 环境重置
```bash
# 重置测试环境
curl -X POST http://$BOARD_IP:5001/api/test/env/reset \
     -H "Content-Type: application/json" \
     -d '{
       "clear_data": true,
       "reset_config": false,
       "restart_services": true
     }'

# 验证重置结果
curl http://$BOARD_IP:5001/api/test/env/status
```

## 📁 阶段8: 数据验证测试

### 8.1 检查数据文件
```bash
# 检查传感器数据文件
ls -la test_data/sensors/current/

# 查看最新的传感器数据
cat test_data/sensors/current/sensor_data_$(date +%Y%m%d).json | tail -5

# 检查设备状态文件
cat test_data/devices/current/device_status.json

# 检查错误日志
cat test_data/logs/error_$(date +%Y%m%d).log
```

### 8.2 数据格式验证
```bash
# 验证JSON格式
python -m json.tool test_data/sensors/current/sensor_data_$(date +%Y%m%d).json

# 检查数据完整性
grep -c "co2" test_data/sensors/current/sensor_data_$(date +%Y%m%d).json
grep -c "temperature" test_data/sensors/current/sensor_data_$(date +%Y%m%d).json
```

## 🔍 阶段9: 错误处理测试

### 9.1 无效数据测试
```bash
# 发送无效的传感器类型
curl -X POST http://$BOARD_IP:5001/api/data/inject/sensor \
     -H "Content-Type: application/json" \
     -d '{
       "sensor_type": "invalid_sensor",
       "value": 100
     }'

# 预期响应：400错误

# 发送无效的数值
curl -X POST http://$BOARD_IP:5001/api/data/inject/sensor \
     -H "Content-Type: application/json" \
     -d '{
       "sensor_type": "co2",
       "value": "invalid_value"
     }'

# 预期响应：400错误
```

### 9.2 缺失参数测试
```bash
# 缺失必要参数
curl -X POST http://$BOARD_IP:5001/api/data/inject/sensor \
     -H "Content-Type: application/json" \
     -d '{
       "value": 450
     }'

# 预期响应：400错误，提示缺失sensor_type
```

### 9.3 系统限制测试
```bash
# 测试大量数据注入
for i in {1..100}; do
  curl -X POST http://$BOARD_IP:5001/api/data/inject/sensor \
       -H "Content-Type: application/json" \
       -d "{\"sensor_type\": \"co2\", \"value\": $((400 + i))}" &
done
wait

# 检查系统状态
curl http://$BOARD_IP:5001/api/system/status
```

## 📊 阶段10: 性能压力测试

### 10.1 并发请求测试
```bash
# 使用ab工具进行压力测试（如果可用）
ab -n 1000 -c 10 http://$BOARD_IP:5001/api/health

# 或使用curl进行简单并发测试
for i in {1..50}; do
  curl http://$BOARD_IP:5001/api/system/status &
done
wait
```

### 10.2 长时间运行测试
```bash
# 启动长时间性能监控
curl -X POST http://$BOARD_IP:5001/api/monitor/start \
     -H "Content-Type: application/json" \
     -d '{
       "interval": 5,
       "duration": 600
     }'

# 持续注入数据（10分钟）
for i in {1..120}; do
  curl -X POST http://$BOARD_IP:5001/api/data/inject/sensor \
       -H "Content-Type: application/json" \
       -d "{\"sensor_type\": \"co2\", \"value\": $((400 + (i % 100)))}"
  sleep 5
done

# 检查最终状态
curl http://$BOARD_IP:5001/api/monitor/data?limit=10
```

## ✅ 测试结果验证

### 预期成功标准：
1. ✅ 所有API接口返回正确的HTTP状态码
2. ✅ 数据注入后能在test_data目录中找到对应文件
3. ✅ MQTT模拟消息正确记录
4. ✅ 性能监控数据完整
5. ✅ 错误处理返回适当的错误信息
6. ✅ 系统在压力测试后仍然稳定运行

### 常见问题排查：
- **连接被拒绝**: 检查服务是否启动，防火墙设置
- **404错误**: 检查URL路径是否正确
- **500错误**: 查看系统日志，检查配置文件
- **数据未保存**: 检查test_data目录权限

## 🤖 自动化测试脚本

为了简化测试过程，我们提供了自动化测试脚本：

### 使用自动化脚本
```bash
# 运行完整测试套件
bash testing_interface/scripts/run_comprehensive_tests.sh

# 运行基础功能测试
bash testing_interface/scripts/run_basic_tests.sh

# 运行性能测试
bash testing_interface/scripts/run_performance_tests.sh
```

### 查看测试报告
```bash
# 查看最新测试报告
cat test_data/reports/test_report_$(date +%Y%m%d).html

# 在浏览器中打开报告
open test_data/reports/test_report_$(date +%Y%m%d).html  # Mac
start test_data/reports/test_report_$(date +%Y%m%d).html # Windows
```

---

**测试完成后记得**:
1. 停止测试服务: `Ctrl+C`
2. 清理测试数据: `rm -rf test_data/*` (可选)
3. 检查系统资源使用情况
4. 查看测试报告了解详细结果
