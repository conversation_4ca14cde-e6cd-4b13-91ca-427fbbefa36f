import serial
import struct
import time
from utils.config_loader import load_config
from utils.logger import get_logger

class WindSpeedSensor:
    def __init__(self, port=None, baudrate=None, timeout=None):
        # 如果未提供参数，则从配置文件加载
        if port is None or baudrate is None or timeout is None:
            config = load_config()
            serial_config = config['serial']
            port = port or serial_config['port']
            baudrate = baudrate or serial_config['baudrate']
            timeout = timeout or serial_config['timeout']
            
        self.ser = serial.Serial(port, baudrate, timeout=timeout)
        if not self.ser.is_open:
            self.ser.open()
        time.sleep(0.1)  # 等待串口初始化

    def read_wind_speed(self, device_address=None):
        # 如果未提供设备地址，则从配置文件加载
        if device_address is None:
            config = load_config()
            device_address = config['sensors']['wind_speed']['device_address']
            
        # 构造请求帧
        request_frame = bytearray([
            device_address,  # 地址码
            0x03,            # 功能码
            0x00, 0x00,      # 起始地址 0000H
            0x00, 0x01,      # 寄存器数量
        ])
        
        # 计算并添加CRC
        crc = self.calculate_crc(request_frame)
        request_frame.append(crc & 0xFF)      # CRC低字节
        request_frame.append((crc >> 8) & 0xFF)  # CRC高字节
        
        # 发送请求
        self.ser.flushInput()  # 清空输入缓冲区
        self.ser.write(request_frame)
        
        # 读取响应
        response = self.ser.read(7)  # 响应帧长度为 7 字节
        
        if len(response) != 7:
            print(f"响应长度错误: 期望7字节, 实际{len(response)}字节")
            return None
        
        # 验证CRC
        received_crc = struct.unpack('<H', response[-2:])[0]
        calculated_crc = self.calculate_crc(response[:-2])
        
        if received_crc != calculated_crc:
            print(f"CRC校验失败: 接收{received_crc:04X}, 计算{calculated_crc:04X}")
            return None
        
        # 解析风速值
        wind_speed_raw = struct.unpack('>H', response[3:5])[0]
        return wind_speed_raw / 10.0  # 转换为实际值

    def calculate_crc(self, data):
        crc = 0xFFFF
        for byte in data:
            crc ^= byte
            for _ in range(8):
                if crc & 0x0001:
                    crc >>= 1
                    crc ^= 0xA001
                else:
                    crc >>= 1
        return crc

    def close(self):
        self.ser.close()

def try_read_wind_speed():
    config = load_config()
    port = config['serial']['port']
    logger = get_logger("ws_sensor")
    try:
        logger.info(f"尝试连接串口: {port}")
        sensor = WindSpeedSensor()
        wind_speed = sensor.read_wind_speed()
        if wind_speed is not None:
            logger.info(f"当前风速: {wind_speed} m/s")
        else:
            logger.warning("无法读取风速数据")
        sensor.close()
    except serial.SerialException as e:
        logger.error(f"串口错误: {e}")
    except Exception as e:
        logger.error(f"连接失败: {e}")

if __name__ == "__main__":
    try_read_wind_speed()