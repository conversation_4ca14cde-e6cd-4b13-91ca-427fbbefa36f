"""
设备健康检查单元测试

测试设备健康检查模块的各项功能。
"""

import pytest
import time
from unittest.mock import Mock, patch, MagicMock
from test.fixtures.test_data import TestDataGenerator
from test.fixtures.config_samples import get_test_config


@pytest.mark.unit
class TestDeviceHealthCheck:
    """设备健康检查测试类"""
    
    def setup_method(self):
        """测试前设置"""
        self.config = get_test_config("device_health")
        self.mock_logger = Mock()
        
    def test_device_health_check_import(self):
        """测试设备健康检查模块导入"""
        try:
            from utils.device_health_check import DeviceHealthCheck
            assert DeviceHealthCheck is not None
        except ImportError as e:
            pytest.skip(f"设备健康检查模块未找到: {e}")
    
    @patch('utils.device_health_check.get_logger')
    def test_device_health_check_initialization(self, mock_get_logger):
        """测试设备健康检查初始化"""
        try:
            from utils.device_health_check import DeviceHealthCheck
            
            mock_get_logger.return_value = self.mock_logger
            
            health_checker = DeviceHealthCheck(self.config)
            
            assert health_checker is not None
            assert health_checker.config == self.config
            assert hasattr(health_checker, 'device_id')
            
        except ImportError:
            pytest.skip("设备健康检查模块未找到")
    
    @patch('utils.device_health_check.psutil.cpu_percent')
    @patch('utils.device_health_check.get_logger')
    def test_get_cpu_usage(self, mock_get_logger, mock_cpu_percent):
        """测试获取CPU使用率"""
        try:
            from utils.device_health_check import DeviceHealthCheck
            
            mock_get_logger.return_value = self.mock_logger
            mock_cpu_percent.return_value = 45.5
            
            health_checker = DeviceHealthCheck(self.config)
            cpu_usage = health_checker.get_cpu_usage()
            
            assert cpu_usage == 45.5
            mock_cpu_percent.assert_called_once()
            
        except ImportError:
            pytest.skip("设备健康检查模块未找到")
    
    @patch('utils.device_health_check.psutil.virtual_memory')
    @patch('utils.device_health_check.get_logger')
    def test_get_memory_usage(self, mock_get_logger, mock_virtual_memory):
        """测试获取内存使用率"""
        try:
            from utils.device_health_check import DeviceHealthCheck
            
            mock_get_logger.return_value = self.mock_logger
            mock_memory = Mock()
            mock_memory.percent = 65.2
            mock_virtual_memory.return_value = mock_memory
            
            health_checker = DeviceHealthCheck(self.config)
            memory_usage = health_checker.get_memory_usage()
            
            assert memory_usage == 65.2
            mock_virtual_memory.assert_called_once()
            
        except ImportError:
            pytest.skip("设备健康检查模块未找到")
    
    @patch('utils.device_health_check.shutil.disk_usage')
    @patch('utils.device_health_check.get_logger')
    def test_get_disk_usage(self, mock_get_logger, mock_disk_usage):
        """测试获取磁盘使用率"""
        try:
            from utils.device_health_check import DeviceHealthCheck
            
            mock_get_logger.return_value = self.mock_logger
            # 模拟磁盘使用情况：总共100GB，已用30GB
            mock_disk_usage.return_value = (100 * 1024**3, 30 * 1024**3, 70 * 1024**3)
            
            health_checker = DeviceHealthCheck(self.config)
            disk_usage = health_checker.get_disk_usage()
            
            assert disk_usage == 30.0  # 30%
            mock_disk_usage.assert_called_once()
            
        except ImportError:
            pytest.skip("设备健康检查模块未找到")
    
    @patch('utils.device_health_check.get_logger')
    def test_check_component_status_normal(self, mock_get_logger):
        """测试组件状态检查 - 正常情况"""
        try:
            from utils.device_health_check import DeviceHealthCheck
            
            mock_get_logger.return_value = self.mock_logger
            
            health_checker = DeviceHealthCheck(self.config)
            
            # 模拟各个检查方法
            health_checker.get_cpu_usage = Mock(return_value=50.0)
            health_checker.get_memory_usage = Mock(return_value=60.0)
            health_checker.get_disk_usage = Mock(return_value=40.0)
            health_checker.get_gpu_usage = Mock(return_value=30.0)
            
            status = health_checker.check_component_status()
            
            assert status is not None
            assert 'cpu' in status
            assert 'memory' in status
            assert 'disk' in status
            assert 'gpu' in status
            
            # 所有组件应该是正常状态（1）
            assert status['cpu'] == 1
            assert status['memory'] == 1
            assert status['disk'] == 1
            assert status['gpu'] == 1
            
        except ImportError:
            pytest.skip("设备健康检查模块未找到")
    
    @patch('utils.device_health_check.get_logger')
    def test_check_component_status_abnormal(self, mock_get_logger):
        """测试组件状态检查 - 异常情况"""
        try:
            from utils.device_health_check import DeviceHealthCheck
            
            mock_get_logger.return_value = self.mock_logger
            
            health_checker = DeviceHealthCheck(self.config)
            
            # 模拟异常情况
            health_checker.get_cpu_usage = Mock(return_value=95.0)  # 超过阈值
            health_checker.get_memory_usage = Mock(return_value=5.0)  # 低于阈值
            health_checker.get_disk_usage = Mock(return_value=5.0)   # 低于阈值
            health_checker.get_gpu_usage = Mock(return_value=95.0)   # 超过阈值
            
            status = health_checker.check_component_status()
            
            assert status is not None
            
            # 异常组件应该是异常状态（2）
            assert status['cpu'] == 2
            assert status['memory'] == 2
            assert status['disk'] == 2
            assert status['gpu'] == 2
            
        except ImportError:
            pytest.skip("设备健康检查模块未找到")
    
    @patch('utils.device_health_check.get_logger')
    def test_generate_check_data(self, mock_get_logger):
        """测试生成检查数据"""
        try:
            from utils.device_health_check import DeviceHealthCheck
            
            mock_get_logger.return_value = self.mock_logger
            
            health_checker = DeviceHealthCheck(self.config)
            
            # 模拟组件状态
            mock_status = {
                'co2': 1,
                'temp': 1,
                'hum': 1,
                'gps': 1,
                'ws': 1,
                'camera': 1,
                'fan': 1,
                'co2device': 1,
                'cpu': 50,
                'memory': 60,
                'disk': 40,
                'gpu': 30
            }
            
            health_checker.check_component_status = Mock(return_value=mock_status)
            
            check_data = health_checker.generate_check_data()
            
            assert check_data is not None
            assert 'time' in check_data
            assert 'devid' in check_data
            assert 'ver' in check_data
            assert 'dir' in check_data
            
            # 检查所有组件状态
            for component in ['co2', 'temp', 'hum', 'gps', 'ws', 'camera', 'fan', 'co2device']:
                assert component in check_data
            
            for metric in ['cpu', 'memory', 'disk', 'gpu']:
                assert metric in check_data
            
        except ImportError:
            pytest.skip("设备健康检查模块未找到")
    
    @patch('utils.device_health_check.get_logger')
    def test_hardware_failure_simulation(self, mock_get_logger):
        """测试硬件故障模拟"""
        try:
            from utils.device_health_check import DeviceHealthCheck
            
            mock_get_logger.return_value = self.mock_logger
            
            health_checker = DeviceHealthCheck(self.config)
            
            # 测试模拟传感器故障
            health_checker.simulate_sensor_failure('cth', True)
            status = health_checker.check_component_status()
            
            # 应该检测到CTH传感器故障
            if 'cth' in status:
                assert status['cth'] == 2  # 异常状态
            
            # 恢复传感器
            health_checker.simulate_sensor_failure('cth', False)
            status = health_checker.check_component_status()
            
            if 'cth' in status:
                assert status['cth'] == 1  # 正常状态
            
        except ImportError:
            pytest.skip("设备健康检查模块未找到")
    
    def test_test_data_generator_device_check_data(self):
        """测试设备检查数据生成器"""
        check_data = TestDataGenerator.generate_device_check_data()
        
        assert check_data is not None
        assert 'time' in check_data
        assert 'devid' in check_data
        assert 'ver' in check_data
        assert 'dir' in check_data
        
        # 检查组件状态字段
        components = ['co2', 'temp', 'hum', 'gps', 'ws', 'camera', 'fan', 'co2device']
        for component in components:
            assert component in check_data
            assert check_data[component] in [1, 2]  # 1: 正常, 2: 异常
        
        # 检查性能指标字段
        metrics = ['cpu', 'memory', 'disk', 'gpu']
        for metric in metrics:
            assert metric in check_data
            assert isinstance(check_data[metric], int)
            assert 0 <= check_data[metric] <= 100
