#!/bin/bash

# IoT传感器监控系统 - 基础功能测试脚本
# 快速验证核心功能是否正常

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置变量
BOARD_IP=${BOARD_IP:-"localhost"}
API_PORT=${API_PORT:-"5001"}
BASE_URL="http://${BOARD_IP}:${API_PORT}"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 测试函数
test_endpoint() {
    local name=$1
    local method=$2
    local endpoint=$3
    local data=$4
    
    echo -n "测试 $name ... "
    
    if [ "$method" = "GET" ]; then
        if curl -s -f "$BASE_URL$endpoint" > /dev/null; then
            echo -e "${GREEN}✅ 通过${NC}"
            return 0
        else
            echo -e "${RED}❌ 失败${NC}"
            return 1
        fi
    else
        if [ -n "$data" ]; then
            if curl -s -f -X "$method" \
                -H "Content-Type: application/json" \
                -d "$data" \
                "$BASE_URL$endpoint" > /dev/null; then
                echo -e "${GREEN}✅ 通过${NC}"
                return 0
            else
                echo -e "${RED}❌ 失败${NC}"
                return 1
            fi
        else
            if curl -s -f -X "$method" "$BASE_URL$endpoint" > /dev/null; then
                echo -e "${GREEN}✅ 通过${NC}"
                return 0
            else
                echo -e "${RED}❌ 失败${NC}"
                return 1
            fi
        fi
    fi
}

# 主测试流程
main() {
    echo "========================================"
    echo "IoT传感器监控系统 - 基础功能测试"
    echo "测试目标: $BASE_URL"
    echo "========================================"
    
    local total=0
    local passed=0
    
    # 基础连接测试
    log_info "基础连接测试"
    if test_endpoint "健康检查" "GET" "/api/health"; then ((passed++)); fi
    ((total++))
    
    if test_endpoint "系统信息" "GET" "/api/info"; then ((passed++)); fi
    ((total++))
    
    if test_endpoint "系统状态" "GET" "/api/system/status"; then ((passed++)); fi
    ((total++))
    
    # 数据注入测试
    log_info "数据注入测试"
    co2_data='{"sensor_type": "co2", "value": 450, "unit": "ppm"}'
    if test_endpoint "CO2数据注入" "POST" "/api/data/inject/sensor" "$co2_data"; then ((passed++)); fi
    ((total++))
    
    temp_data='{"sensor_type": "temperature", "value": 25.5, "unit": "°C"}'
    if test_endpoint "温度数据注入" "POST" "/api/data/inject/sensor" "$temp_data"; then ((passed++)); fi
    ((total++))
    
    # 监控测试
    log_info "性能监控测试"
    monitor_data='{"interval": 2, "duration": 5}'
    if test_endpoint "启动监控" "POST" "/api/monitor/start" "$monitor_data"; then ((passed++)); fi
    ((total++))
    
    sleep 3
    
    if test_endpoint "获取监控数据" "GET" "/api/monitor/data?limit=3"; then ((passed++)); fi
    ((total++))
    
    if test_endpoint "停止监控" "POST" "/api/monitor/stop"; then ((passed++)); fi
    ((total++))
    
    # MQTT模拟测试
    log_info "MQTT模拟测试"
    if test_endpoint "获取MQTT消息" "GET" "/api/mock/mqtt/messages?limit=5"; then ((passed++)); fi
    ((total++))
    
    # 测试会话
    log_info "测试会话管理"
    session_data='{"name": "基础测试会话", "test_type": "basic"}'
    if test_endpoint "开始测试会话" "POST" "/api/test/session/start" "$session_data"; then ((passed++)); fi
    ((total++))
    
    if test_endpoint "获取会话状态" "GET" "/api/test/session/status"; then ((passed++)); fi
    ((total++))
    
    if test_endpoint "结束测试会话" "POST" "/api/test/session/stop"; then ((passed++)); fi
    ((total++))
    
    # 结果汇总
    echo "========================================"
    echo "测试结果:"
    echo "总测试数: $total"
    echo "通过测试: $passed"
    echo "失败测试: $((total - passed))"
    
    if [ "$passed" -eq "$total" ]; then
        log_success "🎉 所有基础测试通过！"
        echo "========================================"
        return 0
    else
        log_error "⚠️  部分测试失败"
        echo "========================================"
        return 1
    fi
}

# 检查服务是否运行
log_info "检查服务连接..."
if ! curl -s "$BASE_URL/api/health" > /dev/null; then
    log_error "无法连接到测试服务，请确保服务已启动："
    echo "python main.py --test-mode --config testing_interface/config/config_test.yaml --test-api --test-port 5001"
    exit 1
fi

log_success "服务连接正常"

# 运行测试
main "$@"
