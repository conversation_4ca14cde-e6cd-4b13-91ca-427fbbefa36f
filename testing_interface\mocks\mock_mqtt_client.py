#!/usr/bin/env python3
"""
模拟MQTT客户端
用于测试模式下完全隔离的MQTT通信模拟
"""

import json
import time
import threading
import queue
import random
import uuid
import logging
from datetime import datetime
from typing import Dict, Any, List, Optional
from enum import Enum

# 模拟ConnectionState枚举
class ConnectionState(Enum):
    DISCONNECTED = "disconnected"
    CONNECTING = "connecting"
    CONNECTED = "connected"
    RECONNECTING = "reconnecting"

class MockMQTTClient:
    """模拟MQTT客户端，完全替代真实MQTT通信"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.mqtt_config = config.get('mqtt', {})
        self.test_config = config.get('test_mode', {})
        self.mock_config = self.test_config.get('mock_responses', {})

        # 初始化logger
        self.logger = logging.getLogger(f"{__name__}.MockMQTTClient")
        
        # 连接状态
        self.connected = True  # 模拟始终连接
        self.connection_state = ConnectionState.CONNECTED
        
        # 设备信息
        self.device_id = config.get('device_binding', {}).get('device_id', 'TEST_DEVICE_001')
        
        # 消息存储
        self.published_messages = []
        self.received_responses = []
        self.message_lock = threading.Lock()
        
        # 队列模拟
        self.sensor_queue = queue.Queue(maxsize=self.mqtt_config.get('sensor_queue_size', 100))
        self.error_queue = queue.Queue(maxsize=self.mqtt_config.get('error_queue_size', 50))
        self.co2_queue = queue.Queue(maxsize=self.mqtt_config.get('co2_queue_size', 20))
        self.check_queue = queue.Queue(maxsize=self.mqtt_config.get('check_queue_size', 50))
        self.mosquito_queue = queue.Queue(maxsize=self.mqtt_config.get('mosquito_queue_size', 50))
        
        # 统计信息
        self.stats = {
            'total_published': 0,
            'successful_published': 0,
            'failed_published': 0,
            'responses_received': 0,
            'start_time': time.time()
        }

        # 传感器数据计数
        self.sensor_data_processed_count = 0
        self.sensor_data_success_count = 0
        self.sensor_data_error_count = 0
        self.sensor_data_total_count = 0
        self.error_data_processed_count = 0

        # 设备操作回调
        self.device_callbacks = {
            "power_on": None,    # 开机回调
            "power_off": None,   # 关机回调
            "restart": None      # 重启回调
        }
        self._handle_component_switch_callback = None
        
        # 模拟响应线程
        self.response_thread = None
        self.running = False
        
        # 初始化
        self._initialize()
    
    def _initialize(self):
        """初始化模拟客户端"""
        self.running = True
        
        # 启动模拟响应线程
        self.response_thread = threading.Thread(target=self._response_loop, daemon=True)
        self.response_thread.start()
        
        print(f"🧪 MockMQTTClient 初始化完成 - 设备ID: {self.device_id}")
    
    def _response_loop(self):
        """模拟服务器响应循环"""
        while self.running:
            try:
                # 模拟响应延迟
                delay = self.mock_config.get('mqtt_response_delay', 0.1)
                time.sleep(delay)
                
                # 处理待响应的消息
                self._generate_mock_responses()
                
            except Exception as e:
                print(f"模拟响应循环错误: {e}")
            
            time.sleep(0.1)
    
    def _generate_mock_responses(self):
        """生成模拟响应"""
        with self.message_lock:
            # 为最近发布的消息生成响应
            recent_messages = [msg for msg in self.published_messages 
                             if not msg.get('response_generated', False) 
                             and time.time() - msg['timestamp'] > 0.1]
            
            for message in recent_messages:
                # 模拟成功率
                success_rate = self.mock_config.get('mqtt_publish_success_rate', 0.95)
                is_success = random.random() < success_rate
                
                if is_success:
                    response = self._create_success_response(message)
                else:
                    response = self._create_error_response(message)
                
                self.received_responses.append(response)
                message['response_generated'] = True
                message['response'] = response
                
                # 更新统计
                if is_success:
                    self.stats['successful_published'] += 1
                else:
                    self.stats['failed_published'] += 1
                
                self.stats['responses_received'] += 1
    
    def _create_success_response(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """创建成功响应"""
        return {
            'devid': self.device_id,
            'code': 200,
            'msg': '数据接收成功',
            'timestamp': time.time(),
            'message_id': message.get('message_id'),
            'data_type': message.get('data_type'),
            'mock_response': True
        }
    
    def _create_error_response(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """创建错误响应"""
        error_codes = [400, 500, 503, 504]
        error_messages = {
            400: '数据格式错误',
            500: '服务器内部错误', 
            503: '服务不可用',
            504: '网关超时'
        }
        
        error_code = random.choice(error_codes)
        return {
            'devid': self.device_id,
            'code': error_code,
            'msg': error_messages[error_code],
            'timestamp': time.time(),
            'message_id': message.get('message_id'),
            'data_type': message.get('data_type'),
            'mock_response': True
        }
    
    def _should_simulate_failure(self) -> bool:
        """判断是否应该模拟失败"""
        failure_rate = self.mock_config.get('server_error_rate', 0.02)
        return random.random() < failure_rate
    
    def _wait_for_message_interval(self):
        """模拟消息发送间隔"""
        interval = self.mqtt_config.get('message_upload_interval', 0.1)
        time.sleep(interval)
    
    def publish_sensor_data(self, data: Dict[str, Any]) -> bool:
        """发布传感器数据（模拟）"""
        return self._publish_message(data, 'sensor_data', 'sensor_topic')
    
    def publish_error_data(self, data: Dict[str, Any]) -> bool:
        """发布错误数据（模拟）"""
        return self._publish_message(data, 'error_data', 'sensor_topic')
    
    def publish_co2_status(self, data: Dict[str, Any]) -> bool:
        """发布CO2状态数据（模拟）"""
        return self._publish_message(data, 'co2_status', 'co2_topic')
    
    def publish_device_check(self, data: Dict[str, Any]) -> bool:
        """发布设备检查数据（模拟）"""
        return self._publish_message(data, 'device_check', 'check_topic')
    
    def publish_mosquito_data_simple(self, data: Dict[str, Any]) -> bool:
        """发布蚊虫检测数据（模拟）"""
        return self._publish_message(data, 'mosquito_data', 'mosquito_topic')
    
    def _publish_message(self, data: Dict[str, Any], data_type: str, topic_key: str) -> bool:
        """通用消息发布方法"""
        try:
            # 模拟发送延迟
            self._wait_for_message_interval()
            
            # 检查是否应该模拟失败
            if self._should_simulate_failure():
                self.stats['failed_published'] += 1
                return False
            
            # 确保设备ID
            if 'devid' not in data or not data['devid']:
                data['devid'] = self.device_id
            
            # 创建消息记录
            message = {
                'message_id': str(uuid.uuid4()),
                'data_type': data_type,
                'topic': self.mqtt_config.get(topic_key, f'/mock/{data_type}').format(device_id=self.device_id),
                'payload': data.copy(),
                'timestamp': time.time(),
                'response_generated': False
            }
            
            # 存储消息
            with self.message_lock:
                self.published_messages.append(message)

                # 限制消息历史长度
                if len(self.published_messages) > 1000:
                    self.published_messages = self.published_messages[-500:]

                # 保存消息到文件
                self._save_messages_to_file()
            
            # 更新统计
            self.stats['total_published'] += 1
            
            return True
            
        except Exception as e:
            print(f"模拟发布消息失败: {e}")
            self.stats['failed_published'] += 1
            return False

    def get_published_messages(self, data_type: Optional[str] = None, limit: int = 100) -> List[Dict[str, Any]]:
        """获取已发布的消息"""
        with self.message_lock:
            messages = self.published_messages.copy()

            if data_type:
                messages = [msg for msg in messages if msg.get('data_type') == data_type]

            return messages[-limit:] if limit > 0 else messages

    def get_responses(self, limit: int = 100) -> List[Dict[str, Any]]:
        """获取模拟响应"""
        with self.message_lock:
            return self.received_responses[-limit:] if limit > 0 else self.received_responses.copy()

    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        current_time = time.time()
        uptime = current_time - self.stats['start_time']

        return {
            **self.stats,
            'uptime_seconds': uptime,
            'messages_per_second': self.stats['total_published'] / uptime if uptime > 0 else 0,
            'success_rate': (self.stats['successful_published'] / self.stats['total_published']
                           if self.stats['total_published'] > 0 else 0),
            'queue_sizes': {
                'sensor_queue': self.sensor_queue.qsize(),
                'error_queue': self.error_queue.qsize(),
                'co2_queue': self.co2_queue.qsize(),
                'check_queue': self.check_queue.qsize(),
                'mosquito_queue': self.mosquito_queue.qsize()
            }
        }

    def clear_history(self):
        """清空消息历史"""
        with self.message_lock:
            self.published_messages.clear()
            self.received_responses.clear()

        # 重置统计（保留启动时间）
        start_time = self.stats['start_time']
        self.stats = {
            'total_published': 0,
            'successful_published': 0,
            'failed_published': 0,
            'responses_received': 0,
            'start_time': start_time
        }

    def simulate_network_failure(self, duration: float = 30.0, failure_rate: float = 1.0):
        """模拟网络故障"""
        def restore_network():
            time.sleep(duration)
            self.mock_config['server_error_rate'] = self.mock_config.get('server_error_rate', 0.02)
            print(f"🔧 网络故障模拟结束，恢复正常")

        # 临时提高错误率
        original_error_rate = self.mock_config.get('server_error_rate', 0.02)
        self.mock_config['server_error_rate'] = failure_rate

        print(f"⚠️ 模拟网络故障 {duration}秒，错误率: {failure_rate}")

        # 启动恢复线程
        restore_thread = threading.Thread(target=restore_network, daemon=True)
        restore_thread.start()

    def simulate_server_error(self, duration: float = 10.0):
        """模拟服务器错误"""
        self.simulate_network_failure(duration, 1.0)

    def simulate_timeout(self, duration: float = 5.0):
        """模拟超时"""
        original_delay = self.mock_config.get('mqtt_response_delay', 0.1)
        self.mock_config['mqtt_response_delay'] = 10.0  # 设置很长的延迟模拟超时

        def restore_delay():
            time.sleep(duration)
            self.mock_config['mqtt_response_delay'] = original_delay
            print(f"🔧 超时模拟结束，恢复正常响应时间")

        print(f"⏱️ 模拟超时 {duration}秒")
        restore_thread = threading.Thread(target=restore_delay, daemon=True)
        restore_thread.start()

    def stop(self):
        """停止模拟客户端"""
        self.running = False
        self.connected = False
        self.connection_state = ConnectionState.DISCONNECTED

        if self.response_thread and self.response_thread.is_alive():
            self.response_thread.join(timeout=1)

        print("🧪 MockMQTTClient 已停止")

    # 兼容性方法，保持与真实MQTT客户端接口一致
    def start(self):
        """启动客户端（模拟）"""
        if not self.running:
            self._initialize()

        # 确保连接状态为已连接
        self.connected = True
        self.connection_state = ConnectionState.CONNECTED
        self.logger.info("🧪 模拟MQTT客户端：已设置为连接状态")

    def disconnect(self):
        """断开连接（模拟）"""
        self.connected = False
        self.connection_state = ConnectionState.DISCONNECTED

    def reconnect(self):
        """重新连接（模拟）"""
        self.connected = True
        self.connection_state = ConnectionState.CONNECTED

    # 保存模拟数据到文件
    def save_mock_data(self, file_path: str = None):
        """保存模拟数据到文件"""
        if file_path is None:
            file_path = "test_data/mock_data/mqtt_messages.json"

        try:
            mock_data = {
                'messages': self.get_published_messages(),
                'responses': self.get_responses(),
                'stats': self.get_stats(),
                'saved_at': datetime.now().isoformat()
            }

            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(mock_data, f, ensure_ascii=False, indent=2)

            print(f"📁 模拟数据已保存到: {file_path}")
            return True

        except Exception as e:
            print(f"❌ 保存模拟数据失败: {e}")
            return False

    def register_device_callbacks(self, power_on_cb=None, power_off_cb=None, restart_cb=None, component_switch_cb=None):
        """注册设备操作回调函数"""
        if power_on_cb is not None:
            self.device_callbacks["power_on"] = power_on_cb

        if power_off_cb is not None:
            self.device_callbacks["power_off"] = power_off_cb

        if restart_cb is not None:
            self.device_callbacks["restart"] = restart_cb

        if component_switch_cb is not None:
            self._handle_component_switch_callback = component_switch_cb

        self.logger.info("🧪 模拟MQTT客户端：已注册设备操作回调函数")

    def set_component_ready(self, component_name: str, ready: bool = True):
        """设置组件就绪状态"""
        self.logger.info(f"🧪 模拟MQTT客户端：组件 {component_name} 就绪状态设置为 {ready}")

    def update_sensor_counts(self, processed_count: int, normal_count: int, error_count: int):
        """更新传感器数据计数

        Args:
            processed_count: 已处理的数据总数
            normal_count: 正常处理的数据数量
            error_count: 错误的数据数量
        """
        self.sensor_data_processed_count = processed_count
        self.sensor_data_success_count = normal_count
        self.sensor_data_error_count = error_count
        self.logger.debug(f"🧪 模拟MQTT客户端：更新传感器计数 - 总数: {processed_count}, 正常: {normal_count}, 错误: {error_count}")

    def report_power_off_status(self):
        """设备主动上报关机状态"""
        self.logger.info("🧪 模拟MQTT客户端：模拟上报设备关机状态")
        return True

    def _archive_uploaded_sensor_data(self):
        """归档已上传的传感器数据"""
        self.logger.info("🧪 模拟MQTT客户端：模拟归档已上传的传感器数据")
        return True

    def _archive_uploaded_errors(self):
        """归档已上传的错误数据"""
        self.logger.info("🧪 模拟MQTT客户端：模拟归档已上传的错误数据")
        return True

    def publish_mosquito_data_simple(self, data: Dict[str, Any]) -> bool:
        """发布蚊子检测数据（模拟）"""
        self.logger.info("🧪 模拟MQTT客户端：模拟发布蚊子检测数据")
        return self._publish_message(data, 'mosquito_data', 'mosquito_topic')

    def _archive_uploaded_co2_errors(self):
        """归档已上传的CO2错误数据"""
        self.logger.info("🧪 模拟MQTT客户端：模拟归档已上传的CO2错误数据")
        return True

    def _save_messages_to_file(self):
        """保存消息到文件"""
        try:
            import os
            import json

            # 确保目录存在
            messages_file = "test_data/mock_data/mqtt_messages.json"
            os.makedirs(os.path.dirname(messages_file), exist_ok=True)

            # 保存最近的100条消息
            recent_messages = self.published_messages[-100:] if len(self.published_messages) > 100 else self.published_messages

            with open(messages_file, 'w', encoding='utf-8') as f:
                json.dump(recent_messages, f, indent=2, ensure_ascii=False, default=str)

        except Exception as e:
            self.logger.warning(f"保存MQTT消息到文件失败: {e}")
