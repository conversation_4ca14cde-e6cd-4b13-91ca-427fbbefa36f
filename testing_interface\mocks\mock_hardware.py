#!/usr/bin/env python3
"""
模拟硬件组件
用于测试模式下完全替代真实硬件操作
"""

import time
import random
import threading
from typing import Dict, Any, List, Optional
from datetime import datetime
import json

class MockGPIO:
    """模拟GPIO操作"""
    
    # GPIO模式常量
    BOARD = "BOARD"
    BCM = "BCM"
    
    # GPIO状态常量
    HIGH = 1
    LOW = 0
    
    # GPIO方向常量
    IN = "IN"
    OUT = "OUT"
    
    def __init__(self):
        self.mode = None
        self.warnings = True
        self.pin_states = {}
        self.pin_modes = {}
        self.setup_pins = set()
        
    def setmode(self, mode):
        """设置GPIO模式"""
        self.mode = mode
        print(f"🧪 MockGPIO: 设置模式为 {mode}")
    
    def setwarnings(self, warnings):
        """设置警告"""
        self.warnings = warnings
        print(f"🧪 MockGPIO: 警告设置为 {warnings}")
    
    def setup(self, pin, direction, initial=None):
        """设置GPIO引脚"""
        self.setup_pins.add(pin)
        self.pin_modes[pin] = direction
        if initial is not None:
            self.pin_states[pin] = initial
        else:
            self.pin_states[pin] = self.LOW
        print(f"🧪 MockGPIO: 设置引脚 {pin} 为 {direction}, 初始值: {self.pin_states[pin]}")
    
    def output(self, pin, value):
        """输出GPIO值"""
        if pin not in self.setup_pins:
            print(f"⚠️ MockGPIO: 引脚 {pin} 未设置")
            return
        
        self.pin_states[pin] = value
        print(f"🧪 MockGPIO: 引脚 {pin} 输出 {value}")
    
    def input(self, pin):
        """读取GPIO值"""
        if pin not in self.setup_pins:
            print(f"⚠️ MockGPIO: 引脚 {pin} 未设置")
            return self.LOW
        
        # 模拟一些随机输入变化
        if random.random() < 0.1:  # 10%概率改变状态
            self.pin_states[pin] = 1 - self.pin_states[pin]
        
        return self.pin_states.get(pin, self.LOW)
    
    def cleanup(self):
        """清理GPIO"""
        self.setup_pins.clear()
        self.pin_states.clear()
        self.pin_modes.clear()
        print("🧪 MockGPIO: 清理完成")
    
    def get_pin_states(self):
        """获取所有引脚状态"""
        return self.pin_states.copy()

class MockSerial:
    """模拟串口通信"""
    
    def __init__(self, port, baudrate=9600, timeout=1):
        self.port = port
        self.baudrate = baudrate
        self.timeout = timeout
        self.is_open = False
        self.buffer = b""
        
        # 模拟传感器数据
        self.sensor_data = {
            'temperature': 25.0,
            'humidity': 60.0,
            'co2': 400,
            'wind_speed': 2.5
        }
        
    def open(self):
        """打开串口"""
        self.is_open = True
        print(f"🧪 MockSerial: 打开串口 {self.port} (波特率: {self.baudrate})")
    
    def close(self):
        """关闭串口"""
        self.is_open = False
        print(f"🧪 MockSerial: 关闭串口 {self.port}")
    
    def write(self, data):
        """写入数据"""
        if not self.is_open:
            raise Exception("串口未打开")
        
        print(f"🧪 MockSerial: 写入数据 {data}")
        return len(data)
    
    def read(self, size=1):
        """读取数据"""
        if not self.is_open:
            raise Exception("串口未打开")
        
        # 模拟传感器响应
        response = self._generate_sensor_response()
        return response[:size]
    
    def readline(self):
        """读取一行数据"""
        if not self.is_open:
            raise Exception("串口未打开")
        
        return self._generate_sensor_response() + b'\n'
    
    def _generate_sensor_response(self):
        """生成模拟传感器响应"""
        # 添加一些随机变化
        temp_variation = random.uniform(-2, 2)
        humidity_variation = random.uniform(-5, 5)
        co2_variation = random.randint(-50, 50)
        wind_variation = random.uniform(-0.5, 0.5)
        
        self.sensor_data['temperature'] = max(0, min(60, 
            self.sensor_data['temperature'] + temp_variation))
        self.sensor_data['humidity'] = max(0, min(100, 
            self.sensor_data['humidity'] + humidity_variation))
        self.sensor_data['co2'] = max(0, min(5000, 
            self.sensor_data['co2'] + co2_variation))
        self.sensor_data['wind_speed'] = max(0, min(30, 
            self.sensor_data['wind_speed'] + wind_variation))
        
        # 模拟Modbus响应格式
        response = f"{self.sensor_data['temperature']:.1f},{self.sensor_data['humidity']:.1f},{self.sensor_data['co2']},{self.sensor_data['wind_speed']:.1f}"
        return response.encode('utf-8')
    
    def in_waiting(self):
        """返回等待读取的字节数"""
        return random.randint(0, 10)

class MockGPS:
    """模拟GPS模块"""
    
    def __init__(self):
        # 模拟固定位置（可以配置）
        self.latitude = 39.9042  # 北京
        self.longitude = 116.4074
        self.altitude = 50.0
        self.satellites = 8
        self.fix_quality = 1
        
    def get_position(self):
        """获取GPS位置"""
        # 添加小幅度随机变化模拟GPS漂移
        lat_drift = random.uniform(-0.0001, 0.0001)
        lon_drift = random.uniform(-0.0001, 0.0001)
        alt_drift = random.uniform(-1, 1)
        
        return {
            'latitude': self.latitude + lat_drift,
            'longitude': self.longitude + lon_drift,
            'altitude': self.altitude + alt_drift,
            'satellites': random.randint(6, 12),
            'fix_quality': random.choice([1, 2]),  # 1=GPS, 2=DGPS
            'timestamp': datetime.now().isoformat()
        }

class MockSensorController:
    """模拟传感器控制器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.gpio = MockGPIO()
        self.serial_ports = {}
        self.gps = MockGPS()
        
        # 传感器状态
        self.sensors_enabled = True
        self.fan_running = False
        self.pump_running = False
        self.heater_running = False
        
        # 模拟传感器数据
        self.sensor_readings = {
            'cth': {
                'temperature': 25.0,
                'humidity': 60.0,
                'co2': 400
            },
            'wind_speed': 2.5,
            'gps': self.gps.get_position()
        }
        
        # 启动数据更新线程
        self.update_thread = threading.Thread(target=self._update_sensor_data, daemon=True)
        self.running = True
        self.update_thread.start()
    
    def _update_sensor_data(self):
        """定期更新传感器数据"""
        while self.running:
            try:
                # 更新CTH传感器数据
                self._update_cth_data()
                
                # 更新风速数据
                self._update_wind_speed()
                
                # 更新GPS数据
                self.sensor_readings['gps'] = self.gps.get_position()
                
                time.sleep(1)  # 每秒更新一次
                
            except Exception as e:
                print(f"传感器数据更新错误: {e}")
    
    def _update_cth_data(self):
        """更新CTH传感器数据"""
        # 模拟环境变化
        temp_change = random.uniform(-0.1, 0.1)
        humidity_change = random.uniform(-0.5, 0.5)
        co2_change = random.randint(-5, 5)
        
        cth = self.sensor_readings['cth']
        cth['temperature'] = max(0, min(60, cth['temperature'] + temp_change))
        cth['humidity'] = max(0, min(100, cth['humidity'] + humidity_change))
        cth['co2'] = max(0, min(5000, cth['co2'] + co2_change))
    
    def _update_wind_speed(self):
        """更新风速数据"""
        wind_change = random.uniform(-0.05, 0.05)
        self.sensor_readings['wind_speed'] = max(0, min(30, 
            self.sensor_readings['wind_speed'] + wind_change))
    
    def get_sensor_data(self, sensor_type: str = None):
        """获取传感器数据"""
        if sensor_type:
            return self.sensor_readings.get(sensor_type)
        return self.sensor_readings.copy()
    
    def control_fan(self, state: bool):
        """控制风扇"""
        self.fan_running = state
        pin = self.config.get('gpio_pins', {}).get('fan_pin', 18)
        self.gpio.output(pin, self.gpio.HIGH if state else self.gpio.LOW)
        print(f"🧪 风扇 {'启动' if state else '停止'}")
    
    def control_pump(self, state: bool):
        """控制水泵"""
        self.pump_running = state
        pin = self.config.get('gpio_pins', {}).get('pump_pin', 16)
        self.gpio.output(pin, self.gpio.HIGH if state else self.gpio.LOW)
        print(f"🧪 水泵 {'启动' if state else '停止'}")
    
    def control_heater(self, state: bool):
        """控制加热器"""
        self.heater_running = state
        pin = self.config.get('gpio_pins', {}).get('heater_pin', 22)
        self.gpio.output(pin, self.gpio.HIGH if state else self.gpio.LOW)
        print(f"🧪 加热器 {'启动' if state else '停止'}")
    
    def get_device_status(self):
        """获取设备状态"""
        return {
            'sensors_enabled': self.sensors_enabled,
            'fan_running': self.fan_running,
            'pump_running': self.pump_running,
            'heater_running': self.heater_running,
            'gpio_states': self.gpio.get_pin_states(),
            'timestamp': datetime.now().isoformat()
        }
    
    def stop(self):
        """停止模拟控制器"""
        self.running = False
        if self.update_thread.is_alive():
            self.update_thread.join(timeout=1)
        
        # 关闭所有设备
        self.control_fan(False)
        self.control_pump(False)
        self.control_heater(False)
        
        # 清理GPIO
        self.gpio.cleanup()
        
        print("🧪 MockSensorController 已停止")
