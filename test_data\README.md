# 测试数据目录结构

这个目录用于存储测试模式下的所有数据，确保与生产环境完全隔离。

## 目录结构

```
test_data/
├── README.md                    # 本文件
├── sensors/                     # 传感器数据目录
│   ├── current/                 # 当前数据
│   │   ├── sensor_data.json     # 传感器数据
│   │   └── sensor_errors.json   # 传感器错误数据
│   └── history/                 # 历史数据
│       ├── sensor_data_archive.json
│       └── sensor_errors_archive.json
├── devices/                     # 设备数据目录
│   ├── current/                 # 当前设备状态
│   │   ├── co2_errors.json      # CO2设备错误
│   │   └── device_status.json   # 设备状态
│   └── history/                 # 历史设备数据
│       └── co2_errors_archive.json
├── check/                       # 设备自检数据
│   ├── pending/                 # 待上传的自检数据
│   └── uploaded/                # 已上传的自检数据
├── mosquito_shared/             # 蚊虫检测共享数据
│   ├── incoming/                # 待处理的蚊虫检测文件
│   ├── processed/               # 已处理的文件
│   └── failed/                  # 处理失败的文件
├── logs/                        # 测试日志
│   ├── test.log                 # 主测试日志
│   ├── mqtt_mock.log            # 模拟MQTT日志
│   └── api.log                  # API访问日志
├── reports/                     # 测试报告
│   ├── html/                    # HTML格式报告
│   ├── json/                    # JSON格式报告
│   └── pdf/                     # PDF格式报告
├── mock_data/                   # 模拟数据
│   ├── mqtt_messages.json       # 模拟MQTT消息
│   ├── server_responses.json    # 模拟服务器响应
│   └── test_scenarios.json      # 测试场景数据
└── temp/                        # 临时文件
    ├── uploads/                 # 上传临时文件
    └── processing/              # 处理中的临时文件
```

## 数据隔离说明

1. **完全隔离**: 测试数据不会影响生产环境的 `data/` 目录
2. **结构一致**: 目录结构与生产环境保持一致，确保代码兼容性
3. **自动清理**: 测试结束后可以安全删除整个 `test_data/` 目录
4. **权限控制**: 测试模式下只能访问此目录下的数据

## 使用说明

- 启动测试模式时，系统会自动创建所需的子目录
- 所有测试数据都会保存在对应的子目录中
- 测试结束后，可以通过API接口清理测试数据
- 报告文件会保存在 `reports/` 目录中，支持多种格式
