"""
测试配置样例

提供各种测试场景所需的配置文件样例。
"""

# 基础测试配置
BASE_TEST_CONFIG = {
    "serial": {
        "port": "/dev/ttyUSB0",
        "baudrate": 9600,
        "timeout": 1,
        "retry_count": 3,
        "retry_delay": 1
    },
    "sensors": {
        "cth": {
            "device_address": 0x01,
            "temperature_min": 0,
            "temperature_max": 60,
            "humidity_min": 0,
            "humidity_max": 95,
            "co2_min": 0,
            "co2_max": 50000
        },
        "gps": {
            "device_address": 0x02
        },
        "wind_speed": {
            "device_address": 0x04,
            "min": 0,
            "max": 60
        }
    },
    "mqtt": {
        "broker": "test.mqtt.broker",
        "port": 1883,
        "username": "test_user",
        "password": "test_pass",
        "device_id": "TEST_DEVICE_001",
        "keep_alive": 60,
        "connection_timeout": 30,
        "enable_mqtt_connection": False,  # 测试时禁用真实连接
        "verbose_logging": True
    },
    "data_storage": {
        "data_dir": "test_data",
        "sensors_dir": "sensors",
        "devices_dir": "devices",
        "check_dir": "check",
        "max_pending": 100,
        "max_history": 1000
    },
    "data_collection": {
        "interval": 60
    },
    "device_health": {
        "check_interval": 300,
        "cpu_threshold": 90,
        "memory_threshold": 10,
        "disk_threshold": 10,
        "gpu_threshold": 90
    },
    "cache": {
        "max_sensor_cache_size": 100,
        "max_error_cache_size": 50,
        "cache_max_size": 10,
        "flush_interval": 5
    }
}

# 性能测试配置
PERFORMANCE_TEST_CONFIG = {
    **BASE_TEST_CONFIG,
    "data_collection": {
        "interval": 1  # 1秒采集间隔，用于性能测试
    },
    "cache": {
        "max_sensor_cache_size": 1000,
        "max_error_cache_size": 500,
        "cache_max_size": 50,
        "flush_interval": 1
    },
    "mqtt": {
        **BASE_TEST_CONFIG["mqtt"],
        "sensor_queue_size": 1000,
        "error_queue_size": 500,
        "co2_queue_size": 100,
        "check_queue_size": 200
    }
}

# 压力测试配置
STRESS_TEST_CONFIG = {
    **BASE_TEST_CONFIG,
    "data_collection": {
        "interval": 0.1  # 100ms采集间隔，用于压力测试
    },
    "cache": {
        "max_sensor_cache_size": 10000,
        "max_error_cache_size": 5000,
        "cache_max_size": 100,
        "flush_interval": 0.5
    },
    "mqtt": {
        **BASE_TEST_CONFIG["mqtt"],
        "sensor_queue_size": 10000,
        "error_queue_size": 5000,
        "co2_queue_size": 1000,
        "check_queue_size": 2000
    }
}

# 离线测试配置
OFFLINE_TEST_CONFIG = {
    **BASE_TEST_CONFIG,
    "mqtt": {
        **BASE_TEST_CONFIG["mqtt"],
        "enable_mqtt_connection": False,
        "mock_mode": True,
        "mock_reply_delay": 0.1,
        "mock_success_rate": 1.0
    },
    "data_storage": {
        **BASE_TEST_CONFIG["data_storage"],
        "data_dir": "test_data_offline"
    }
}

# 错误模拟配置
ERROR_SIMULATION_CONFIG = {
    **BASE_TEST_CONFIG,
    "mqtt": {
        **BASE_TEST_CONFIG["mqtt"],
        "mock_mode": True,
        "mock_reply_delay": 0.5,
        "mock_success_rate": 0.5  # 50%成功率，模拟网络不稳定
    },
    "sensors": {
        **BASE_TEST_CONFIG["sensors"],
        "error_simulation": {
            "cth_error_rate": 0.1,    # 10%错误率
            "gps_error_rate": 0.05,   # 5%错误率
            "ws_error_rate": 0.08     # 8%错误率
        }
    }
}

# 最小配置（用于测试配置验证）
MINIMAL_CONFIG = {
    "serial": {
        "port": "/dev/ttyUSB0",
        "baudrate": 9600
    },
    "sensors": {
        "cth": {"device_address": 0x01}
    },
    "mqtt": {
        "broker": "localhost",
        "port": 1883,
        "device_id": "TEST_DEVICE"
    },
    "data_storage": {
        "data_dir": "test_data"
    }
}

# 无效配置（用于测试错误处理）
INVALID_CONFIGS = {
    "missing_serial": {
        "sensors": {"cth": {"device_address": 0x01}},
        "mqtt": {"broker": "localhost"},
        "data_storage": {"data_dir": "test_data"}
    },
    "invalid_serial_port": {
        **MINIMAL_CONFIG,
        "serial": {"port": "", "baudrate": 9600}
    },
    "invalid_baudrate": {
        **MINIMAL_CONFIG,
        "serial": {"port": "/dev/ttyUSB0", "baudrate": "invalid"}
    },
    "missing_mqtt_broker": {
        **MINIMAL_CONFIG,
        "mqtt": {"port": 1883, "device_id": "TEST_DEVICE"}
    },
    "invalid_mqtt_port": {
        **MINIMAL_CONFIG,
        "mqtt": {"broker": "localhost", "port": "invalid", "device_id": "TEST_DEVICE"}
    },
    "missing_data_dir": {
        **MINIMAL_CONFIG,
        "data_storage": {}
    }
}

# 蚊子检测配置
MOSQUITO_TEST_CONFIG = {
    **BASE_TEST_CONFIG,
    "mosquito_detection": {
        "enabled": True,
        "shared_data_path": "test_shared_data",
        "incoming_dir": "incoming",
        "processed_dir": "processed",
        "failed_dir": "failed",
        "monitor_interval": 1,
        "file_timeout": 10
    },
    "mqtt": {
        **BASE_TEST_CONFIG["mqtt"],
        "mosquito_topic": "xcc/attr/mosqutio/{device_id}",
        "mosquito_reply_topic": "/xxc/once/mosqutio/{device_id}/reply"
    }
}

# CO2控制器测试配置
CO2_CONTROLLER_TEST_CONFIG = {
    **BASE_TEST_CONFIG,
    "co2_controller": {
        "enabled": True,
        "threshold_high": 1000,
        "threshold_low": 800,
        "control_interval": 30,
        "ventilation_duration": 60
    }
}

# 设备健康检查测试配置
DEVICE_HEALTH_TEST_CONFIG = {
    **BASE_TEST_CONFIG,
    "device_health": {
        "enabled": True,
        "check_interval": 10,  # 10秒检查间隔，用于快速测试
        "cpu_threshold": 90,
        "memory_threshold": 10,
        "disk_threshold": 10,
        "gpu_threshold": 90,
        "battery_threshold": 20,
        "battery_min_voltage": 10.5,
        "battery_max_voltage": 12.0
    }
}


def get_test_config(config_type: str = "base"):
    """获取指定类型的测试配置"""
    configs = {
        "base": BASE_TEST_CONFIG,
        "performance": PERFORMANCE_TEST_CONFIG,
        "stress": STRESS_TEST_CONFIG,
        "offline": OFFLINE_TEST_CONFIG,
        "error_simulation": ERROR_SIMULATION_CONFIG,
        "minimal": MINIMAL_CONFIG,
        "mosquito": MOSQUITO_TEST_CONFIG,
        "co2_controller": CO2_CONTROLLER_TEST_CONFIG,
        "device_health": DEVICE_HEALTH_TEST_CONFIG
    }
    
    return configs.get(config_type, BASE_TEST_CONFIG).copy()


def get_invalid_config(config_type: str):
    """获取指定类型的无效配置"""
    return INVALID_CONFIGS.get(config_type, {}).copy()
