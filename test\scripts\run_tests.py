#!/usr/bin/env python3
"""
测试执行脚本

提供各种测试执行选项和报告生成功能。
"""

import os
import sys
import argparse
import subprocess
from pathlib import Path


def run_command(cmd, cwd=None):
    """执行命令并返回结果"""
    print(f"执行命令: {cmd}")
    try:
        result = subprocess.run(
            cmd, 
            shell=True, 
            cwd=cwd,
            capture_output=True, 
            text=True, 
            encoding='utf-8'
        )
        
        if result.stdout:
            print(result.stdout)
        if result.stderr:
            print(result.stderr, file=sys.stderr)
            
        return result.returncode == 0
    except Exception as e:
        print(f"命令执行失败: {e}", file=sys.stderr)
        return False


def run_unit_tests(verbose=False, coverage=False):
    """运行单元测试"""
    print("=" * 50)
    print("运行单元测试")
    print("=" * 50)
    
    cmd = "python -m pytest test/unit/"
    if verbose:
        cmd += " -v"
    if coverage:
        cmd += " --cov=. --cov-report=html --cov-report=term-missing"
    
    return run_command(cmd)


def run_integration_tests(verbose=False):
    """运行集成测试"""
    print("=" * 50)
    print("运行集成测试")
    print("=" * 50)
    
    cmd = "python -m pytest test/integration/"
    if verbose:
        cmd += " -v"
    
    return run_command(cmd)


def run_performance_tests(verbose=False):
    """运行性能测试"""
    print("=" * 50)
    print("运行性能测试")
    print("=" * 50)
    
    cmd = "python -m pytest test/performance/ --benchmark-only"
    if verbose:
        cmd += " -v"
    
    return run_command(cmd)


def run_stress_tests(verbose=False):
    """运行压力测试"""
    print("=" * 50)
    print("运行压力测试")
    print("=" * 50)
    
    cmd = "python -m pytest test/stress/"
    if verbose:
        cmd += " -v"
    
    return run_command(cmd)


def run_api_tests(verbose=False):
    """运行API测试"""
    print("=" * 50)
    print("运行API测试")
    print("=" * 50)
    
    cmd = "python -m pytest test/api/"
    if verbose:
        cmd += " -v"
    
    return run_command(cmd)


def run_all_tests(verbose=False, coverage=False):
    """运行所有测试"""
    print("=" * 50)
    print("运行所有测试")
    print("=" * 50)
    
    cmd = "python -m pytest test/"
    if verbose:
        cmd += " -v"
    if coverage:
        cmd += " --cov=. --cov-report=html --cov-report=term-missing"
    
    return run_command(cmd)


def install_test_dependencies():
    """安装测试依赖"""
    print("=" * 50)
    print("安装测试依赖")
    print("=" * 50)
    
    return run_command("pip install -r test/requirements.txt")


def generate_test_report():
    """生成测试报告"""
    print("=" * 50)
    print("生成测试报告")
    print("=" * 50)
    
    cmd = "python -m pytest test/ --html=test_report.html --self-contained-html"
    return run_command(cmd)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="传感器系统测试执行脚本")
    
    parser.add_argument(
        "test_type",
        choices=["unit", "integration", "performance", "stress", "api", "all"],
        help="要运行的测试类型"
    )
    
    parser.add_argument(
        "-v", "--verbose",
        action="store_true",
        help="详细输出"
    )
    
    parser.add_argument(
        "-c", "--coverage",
        action="store_true",
        help="生成覆盖率报告（仅适用于unit和all）"
    )
    
    parser.add_argument(
        "--install-deps",
        action="store_true",
        help="安装测试依赖"
    )
    
    parser.add_argument(
        "--report",
        action="store_true",
        help="生成HTML测试报告"
    )
    
    args = parser.parse_args()
    
    # 切换到项目根目录
    project_root = Path(__file__).parent.parent.parent
    os.chdir(project_root)
    
    success = True
    
    # 安装依赖
    if args.install_deps:
        success = install_test_dependencies() and success
    
    # 运行测试
    if args.test_type == "unit":
        success = run_unit_tests(args.verbose, args.coverage) and success
    elif args.test_type == "integration":
        success = run_integration_tests(args.verbose) and success
    elif args.test_type == "performance":
        success = run_performance_tests(args.verbose) and success
    elif args.test_type == "stress":
        success = run_stress_tests(args.verbose) and success
    elif args.test_type == "api":
        success = run_api_tests(args.verbose) and success
    elif args.test_type == "all":
        success = run_all_tests(args.verbose, args.coverage) and success
    
    # 生成报告
    if args.report:
        success = generate_test_report() and success
    
    # 输出结果
    print("\n" + "=" * 50)
    if success:
        print("✅ 所有测试执行完成")
        sys.exit(0)
    else:
        print("❌ 测试执行过程中出现错误")
        sys.exit(1)


def run_specific_test_file(test_file, verbose=True, coverage=False):
    """运行特定测试文件"""
    cmd = ["python", "-m", "pytest", test_file]

    if verbose:
        cmd.append("-v")

    if coverage:
        cmd.extend(["--cov=.", "--cov-report=html", "--cov-report=term"])

    print(f"运行测试文件: {test_file}")
    result = subprocess.run(cmd, capture_output=True, text=True)

    print("STDOUT:")
    print(result.stdout)
    if result.stderr:
        print("STDERR:")
        print(result.stderr)

    return result.returncode == 0


def run_test_suite_with_markers(markers, verbose=True, coverage=False):
    """根据标记运行测试套件"""
    cmd = ["python", "-m", "pytest"]

    for marker in markers:
        cmd.extend(["-m", marker])

    if verbose:
        cmd.append("-v")

    if coverage:
        cmd.extend(["--cov=.", "--cov-report=html", "--cov-report=term"])

    cmd.append("test/")

    print(f"运行标记为 {', '.join(markers)} 的测试")
    result = subprocess.run(cmd, capture_output=True, text=True)

    print("STDOUT:")
    print(result.stdout)
    if result.stderr:
        print("STDERR:")
        print(result.stderr)

    return result.returncode == 0


def generate_test_report():
    """生成测试报告"""
    cmd = [
        "python", "-m", "pytest",
        "test/",
        "--html=test_report.html",
        "--self-contained-html",
        "--cov=.",
        "--cov-report=html:htmlcov",
        "--cov-report=term",
        "--junit-xml=test_results.xml"
    ]

    print("生成详细测试报告...")
    result = subprocess.run(cmd, capture_output=True, text=True)

    if result.returncode == 0:
        print("测试报告生成成功:")
        print("- HTML报告: test_report.html")
        print("- 覆盖率报告: htmlcov/index.html")
        print("- JUnit XML: test_results.xml")
    else:
        print("测试报告生成失败")
        print(result.stderr)

    return result.returncode == 0


if __name__ == "__main__":
    main()
