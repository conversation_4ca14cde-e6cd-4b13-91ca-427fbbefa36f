# 测试接口系统 (Testing Interface)

## 📋 概述

这是专为测试人员设计的HTTP接口测试系统，提供完整的IoT传感器监控系统测试能力。测试人员可以通过HTTP接口进行功能测试、性能测试和压力测试，无需编写Python代码。

## 🏗️ 目录结构

```
testing_interface/
├── api/                    # HTTP测试接口
│   ├── testing_api.py     # Flask测试API服务器
│   └── __init__.py
├── mocks/                  # 模拟组件
│   ├── mock_mqtt_client.py # MQTT客户端模拟
│   ├── mock_hardware.py   # 硬件组件模拟
│   └── __init__.py
├── config/                 # 测试配置
│   ├── config_test.yaml   # 测试环境配置文件
│   └── README.md
├── docs/                   # 文档
│   ├── api_reference.md   # API接口文档
│   ├── testing_guide.md   # 测试指南
│   └── examples.md        # 使用示例
└── README.md              # 本文件
```

## 🚀 快速开始

### 1. 启动测试环境

```bash
# 启动测试模式（包含HTTP测试接口）
python main.py --test-mode --config config/config_test.yaml --test-api --test-port 8080
```

### 2. 访问测试接口

- **API文档页面**: http://localhost:8080/
- **系统状态**: http://localhost:8080/api/system/status
- **健康检查**: http://localhost:8080/api/health

### 3. 基本测试流程

1. **检查系统状态**
   ```bash
   curl http://localhost:8080/api/system/status
   ```

2. **开始测试会话**
   ```bash
   curl -X POST http://localhost:8080/api/test/session/start \
        -H "Content-Type: application/json" \
        -d '{"name": "功能测试", "description": "传感器数据采集测试"}'
   ```

3. **注入测试数据**
   ```bash
   curl -X POST http://localhost:8080/api/data/inject/sensor \
        -H "Content-Type: application/json" \
        -d '{"sensor_type": "co2", "value": 450, "unit": "ppm"}'
   ```

4. **监控系统性能**
   ```bash
   curl -X POST http://localhost:8080/api/monitor/start
   curl http://localhost:8080/api/monitor/data
   ```

5. **结束测试会话**
   ```bash
   curl -X POST http://localhost:8080/api/test/session/stop
   ```

## 🔧 核心功能

### 系统控制
- ✅ 系统状态查询
- ✅ 组件启停控制
- ✅ 电源管理
- ✅ 传感器控制

### 数据注入
- ✅ 传感器数据注入
- ✅ 错误数据注入
- ✅ 批量数据注入
- ✅ MQTT消息模拟

### 性能监控
- ✅ 系统资源监控 (CPU, 内存, 磁盘)
- ✅ 实时性能数据
- ✅ 组件健康状态
- ✅ 数据处理统计

### 测试管理
- ✅ 测试会话管理
- ✅ 测试环境控制
- ✅ 配置管理
- ✅ 结果统计

## 🛡️ 环境隔离

- **完全数据隔离**: 测试数据存储在独立的 `test_data/` 目录
- **MQTT模拟**: 测试数据不会发送到生产MQTT服务器
- **硬件模拟**: 使用模拟组件替代真实硬件
- **配置隔离**: 使用独立的测试配置文件

## 📊 测试类型

### 功能测试
- 传感器数据采集验证
- MQTT通信功能验证
- 数据处理逻辑验证
- 错误处理机制验证

### 性能测试
- 系统资源使用监控
- 数据处理性能测试
- 响应时间测试
- 吞吐量测试

### 压力测试
- 大量数据注入测试
- 并发请求测试
- 长时间运行测试
- 资源耗尽测试

## 🔗 相关文档

- [API接口文档](docs/api_reference.md) - 详细的API接口说明
- [测试指南](docs/testing_guide.md) - 完整的测试流程指南
- [使用示例](docs/examples.md) - 实际测试场景示例

## ⚠️ 注意事项

1. **仅用于测试**: 此系统仅用于测试环境，不要在生产环境中使用
2. **数据隔离**: 确保测试数据不会影响生产数据
3. **资源监控**: 长时间测试时注意监控系统资源使用
4. **网络隔离**: 测试环境应与生产网络隔离

## 🆚 与pytest测试的区别

| 特性 | HTTP测试接口 | pytest测试 |
|------|-------------|------------|
| 使用者 | 测试人员 | 开发人员 |
| 技能要求 | 无需编程 | 需要Python知识 |
| 测试方式 | HTTP接口 | Python脚本 |
| 测试类型 | 功能/性能/压力 | 单元/集成 |
| 环境隔离 | 完全隔离 | 部分隔离 |
| 实时监控 | 支持 | 有限支持 |

---

**版本**: 1.0.0  
**更新时间**: 2025-07-30  
**维护者**: IoT传感器监控系统开发团队
