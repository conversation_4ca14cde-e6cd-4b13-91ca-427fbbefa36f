"""
pytest配置文件和共享fixtures

这个文件包含了所有测试共享的配置和fixtures，包括：
- 测试配置
- Mock对象
- 测试数据
- 测试环境设置
"""

import os
import sys
import pytest
import tempfile
import shutil
from unittest.mock import Mock, MagicMock
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 测试配置
pytest_plugins = [
    "pytest_asyncio",
    "pytest_mock",
    "pytest_benchmark",
]

# 测试标记
def pytest_configure(config):
    """配置pytest标记"""
    config.addinivalue_line("markers", "unit: 单元测试")
    config.addinivalue_line("markers", "integration: 集成测试")
    config.addinivalue_line("markers", "performance: 性能测试")
    config.addinivalue_line("markers", "stress: 压力测试")
    config.addinivalue_line("markers", "api: API接口测试")
    config.addinivalue_line("markers", "slow: 慢速测试")
    config.addinivalue_line("markers", "hardware: 需要硬件的测试")

@pytest.fixture(scope="session")
def test_config():
    """测试配置fixture"""
    return {
        "test_mode": True,
        "mock_hardware": True,
        "mock_mqtt": True,
        "test_data_dir": "test_data",
        "log_level": "DEBUG"
    }

@pytest.fixture(scope="session")
def temp_dir():
    """临时目录fixture"""
    temp_path = tempfile.mkdtemp(prefix="sensor_test_")
    yield temp_path
    shutil.rmtree(temp_path, ignore_errors=True)

@pytest.fixture
def mock_config():
    """Mock配置对象"""
    config = {
        "serial": {
            "port": "/dev/ttyUSB0",
            "baudrate": 9600,
            "timeout": 1
        },
        "sensors": {
            "cth": {"device_address": 0x01},
            "gps": {"device_address": 0x02},
            "wind_speed": {"device_address": 0x04}
        },
        "mqtt": {
            "broker": "test.mqtt.broker",
            "port": 1883,
            "username": "test_user",
            "password": "test_pass",
            "device_id": "TEST_DEVICE_001"
        },
        "data_storage": {
            "data_dir": "test_data",
            "sensors_dir": "sensors",
            "devices_dir": "devices"
        }
    }
    return config

@pytest.fixture
def mock_serial():
    """Mock串口对象"""
    serial_mock = Mock()
    serial_mock.is_open = True
    serial_mock.read.return_value = b'\x01\x03\x06\x00\x64\x00\x32\x00\x1E\x9C\x8F'
    serial_mock.write.return_value = 8
    return serial_mock

@pytest.fixture
def mock_mqtt_client():
    """Mock MQTT客户端"""
    mqtt_mock = Mock()
    mqtt_mock.connected = True
    mqtt_mock.device_id = "TEST_DEVICE_001"
    mqtt_mock.publish_sensor_data.return_value = True
    mqtt_mock.publish_error_data.return_value = True
    mqtt_mock.publish_co2_status.return_value = True
    return mqtt_mock

@pytest.fixture
def sample_sensor_data():
    """示例传感器数据"""
    return {
        "timestamp": 1640995200,
        "datetime": "2022-01-01 00:00:00",
        "temperature": 25.5,
        "humidity": 60.2,
        "co2": 400,
        "latitude": 39.9042,
        "longitude": 116.4074,
        "wind_speed": 3.2
    }

@pytest.fixture
def sample_error_data():
    """示例错误数据"""
    return {
        "timestamp": 1640995200,
        "sensor_type": "cth",
        "error_type": "通信错误",
        "error_message": "传感器无响应",
        "retry_count": 3
    }

@pytest.fixture(autouse=True)
def setup_test_environment(monkeypatch, temp_dir):
    """自动设置测试环境"""
    # 设置测试数据目录
    monkeypatch.setenv("TEST_DATA_DIR", temp_dir)
    
    # 创建测试目录结构
    os.makedirs(os.path.join(temp_dir, "sensors"), exist_ok=True)
    os.makedirs(os.path.join(temp_dir, "devices"), exist_ok=True)
    os.makedirs(os.path.join(temp_dir, "check"), exist_ok=True)
    
    # 设置测试模式
    monkeypatch.setenv("TESTING", "1")

@pytest.fixture
def mock_logger():
    """Mock日志对象"""
    logger_mock = Mock()
    logger_mock.debug = Mock()
    logger_mock.info = Mock()
    logger_mock.warning = Mock()
    logger_mock.error = Mock()
    logger_mock.critical = Mock()
    return logger_mock
