"""
测试配置管理

统一管理所有测试的配置参数。
"""

import os
import tempfile
from pathlib import Path


class TestConfig:
    """测试配置类"""
    
    # 基础配置
    BASE_CONFIG = {
        'mqtt': {
            'device_id': 'TEST_DEVICE_001',
            'broker_host': 'localhost',
            'broker_port': 1883,
            'username': 'test_user',
            'password': 'test_pass',
            'keepalive': 60,
            'qos': 1
        },
        'sensors': {
            'cth': {
                'port': 'COM1',
                'baudrate': 9600,
                'timeout': 1.0,
                'retry_count': 3
            },
            'gps': {
                'port': 'COM2',
                'baudrate': 9600,
                'timeout': 2.0
            },
            'wind_speed': {
                'port': 'COM3',
                'baudrate': 9600,
                'timeout': 1.0
            }
        },
        'logging': {
            'level': 'DEBUG',
            'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            'file': 'test.log'
        },
        'data': {
            'collection_interval': 10,  # 秒
            'batch_size': 100,
            'max_retries': 3
        }
    }
    
    # 性能测试配置
    PERFORMANCE_CONFIG = {
        **BASE_CONFIG,
        'data': {
            **BASE_CONFIG['data'],
            'collection_interval': 1,  # 更高频率
            'batch_size': 1000,
            'max_retries': 1
        },
        'mqtt': {
            **BASE_CONFIG['mqtt'],
            'qos': 0  # 更低延迟
        }
    }
    
    # 压力测试配置
    STRESS_CONFIG = {
        **BASE_CONFIG,
        'data': {
            **BASE_CONFIG['data'],
            'collection_interval': 0.1,  # 极高频率
            'batch_size': 10000,
            'max_retries': 5
        },
        'mqtt': {
            **BASE_CONFIG['mqtt'],
            'keepalive': 30,
            'qos': 2  # 最高可靠性
        }
    }
    
    # 离线测试配置
    OFFLINE_CONFIG = {
        **BASE_CONFIG,
        'mqtt': {
            **BASE_CONFIG['mqtt'],
            'broker_host': 'offline_broker',  # 不存在的主机
            'broker_port': 9999
        }
    }
    
    # 错误模拟配置
    ERROR_SIMULATION_CONFIG = {
        **BASE_CONFIG,
        'simulation': {
            'sensor_error_rate': 0.1,  # 10%错误率
            'network_error_rate': 0.05,  # 5%网络错误率
            'connection_timeout': 1.0,
            'max_reconnect_attempts': 3
        }
    }
    
    # 设备健康检查配置
    DEVICE_HEALTH_CONFIG = {
        **BASE_CONFIG,
        'health_check': {
            'interval': 30,  # 30秒检查一次
            'cpu_threshold': 80,  # CPU使用率阈值
            'memory_threshold': 85,  # 内存使用率阈值
            'disk_threshold': 90,  # 磁盘使用率阈值
            'temperature_threshold': 70  # 温度阈值
        }
    }
    
    @classmethod
    def get_config(cls, config_type='base'):
        """获取指定类型的配置"""
        config_map = {
            'base': cls.BASE_CONFIG,
            'performance': cls.PERFORMANCE_CONFIG,
            'stress': cls.STRESS_CONFIG,
            'offline': cls.OFFLINE_CONFIG,
            'error_simulation': cls.ERROR_SIMULATION_CONFIG,
            'device_health': cls.DEVICE_HEALTH_CONFIG
        }
        
        config = config_map.get(config_type, cls.BASE_CONFIG).copy()
        
        # 添加测试环境特定配置
        config['test_env'] = {
            'temp_dir': tempfile.mkdtemp(prefix='test_'),
            'log_dir': tempfile.mkdtemp(prefix='test_logs_'),
            'data_dir': tempfile.mkdtemp(prefix='test_data_'),
            'config_type': config_type
        }
        
        return config
    
    @classmethod
    def cleanup_test_env(cls, config):
        """清理测试环境"""
        import shutil
        
        if 'test_env' in config:
            test_env = config['test_env']
            
            for dir_key in ['temp_dir', 'log_dir', 'data_dir']:
                if dir_key in test_env:
                    dir_path = test_env[dir_key]
                    if os.path.exists(dir_path):
                        try:
                            shutil.rmtree(dir_path)
                        except Exception as e:
                            print(f"清理测试目录失败 {dir_path}: {e}")


class TestPaths:
    """测试路径管理"""
    
    def __init__(self):
        self.test_root = Path(__file__).parent
        self.project_root = self.test_root.parent
        
    @property
    def fixtures_dir(self):
        """测试夹具目录"""
        return self.test_root / 'fixtures'
    
    @property
    def utils_dir(self):
        """测试工具目录"""
        return self.test_root / 'utils'
    
    @property
    def unit_tests_dir(self):
        """单元测试目录"""
        return self.test_root / 'unit'
    
    @property
    def integration_tests_dir(self):
        """集成测试目录"""
        return self.test_root / 'integration'
    
    @property
    def performance_tests_dir(self):
        """性能测试目录"""
        return self.test_root / 'performance'
    
    @property
    def stress_tests_dir(self):
        """压力测试目录"""
        return self.test_root / 'stress'
    
    @property
    def api_tests_dir(self):
        """API测试目录"""
        return self.test_root / 'api'
    
    @property
    def scripts_dir(self):
        """测试脚本目录"""
        return self.test_root / 'scripts'
    
    def get_test_data_file(self, filename):
        """获取测试数据文件路径"""
        return self.fixtures_dir / 'data' / filename
    
    def get_config_file(self, filename):
        """获取配置文件路径"""
        return self.fixtures_dir / 'configs' / filename


class TestEnvironment:
    """测试环境管理"""
    
    def __init__(self, config_type='base'):
        self.config_type = config_type
        self.config = TestConfig.get_config(config_type)
        self.paths = TestPaths()
        self._setup_environment()
    
    def _setup_environment(self):
        """设置测试环境"""
        # 设置环境变量
        os.environ['TESTING'] = 'true'
        os.environ['TEST_CONFIG_TYPE'] = self.config_type
        
        # 创建必要的目录
        for dir_path in [
            self.config['test_env']['temp_dir'],
            self.config['test_env']['log_dir'],
            self.config['test_env']['data_dir']
        ]:
            os.makedirs(dir_path, exist_ok=True)
    
    def cleanup(self):
        """清理测试环境"""
        TestConfig.cleanup_test_env(self.config)
        
        # 清理环境变量
        if 'TESTING' in os.environ:
            del os.environ['TESTING']
        if 'TEST_CONFIG_TYPE' in os.environ:
            del os.environ['TEST_CONFIG_TYPE']
    
    def __enter__(self):
        """上下文管理器入口"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.cleanup()


# 全局测试配置实例
test_config = TestConfig()
test_paths = TestPaths()


def get_test_config(config_type='base'):
    """获取测试配置的便捷函数"""
    return TestConfig.get_config(config_type)


def create_test_environment(config_type='base'):
    """创建测试环境的便捷函数"""
    return TestEnvironment(config_type)


# 测试标记定义
TEST_MARKERS = {
    'unit': '单元测试',
    'integration': '集成测试',
    'performance': '性能测试',
    'stress': '压力测试',
    'api': 'API测试',
    'slow': '慢速测试',
    'hardware': '硬件相关测试',
    'network': '网络相关测试',
    'mqtt': 'MQTT相关测试',
    'sensor': '传感器相关测试'
}


# 测试数据常量
TEST_DATA_CONSTANTS = {
    'VALID_TEMPERATURE_RANGE': (-40, 85),
    'VALID_HUMIDITY_RANGE': (0, 100),
    'VALID_CO2_RANGE': (0, 50000),
    'VALID_GPS_LAT_RANGE': (-90, 90),
    'VALID_GPS_LON_RANGE': (-180, 180),
    'VALID_WIND_SPEED_RANGE': (0, 200),
    'DEFAULT_DEVICE_ID': 'TEST_DEVICE_001',
    'DEFAULT_TIMEOUT': 5.0,
    'DEFAULT_RETRY_COUNT': 3
}
