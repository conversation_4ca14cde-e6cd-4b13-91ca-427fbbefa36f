# 开发者测试目录 (Developer Tests)

## 📋 概述

这是专为开发人员设计的pytest测试框架，用于单元测试、集成测试和代码质量保证。

## 🆚 与HTTP测试接口的区别

| 特性 | pytest测试 (test/) | HTTP测试接口 (testing_interface/) |
|------|-------------------|----------------------------------|
| **使用者** | 开发人员 | 测试人员 |
| **技能要求** | Python编程 | HTTP接口调用 |
| **测试粒度** | 函数/类级别 | 系统级别 |
| **测试目的** | 代码质量保证 | 功能验证 |
| **执行方式** | 命令行/IDE | HTTP请求 |
| **报告格式** | HTML/XML | JSON/HTML |

## 🏗️ 目录结构

```
test/                       # 开发者pytest测试
├── unit/                   # 单元测试
├── integration/           # 集成测试
├── performance/           # 性能测试
├── stress/               # 压力测试
├── api/                  # API测试
├── fixtures/             # 测试夹具
├── utils/                # 测试工具
├── scripts/              # 测试脚本
├── conftest.py           # pytest配置
├── pytest.ini           # pytest设置
└── requirements.txt      # 测试依赖

testing_interface/          # 测试人员HTTP接口
├── api/                   # Flask测试API
├── mocks/                 # 模拟组件
├── config/               # 测试配置
└── docs/                 # 接口文档
```

## 🚀 快速开始

### 安装测试依赖
```bash
pip install -r test/requirements.txt
```

### 运行所有测试
```bash
pytest test/
```

### 运行特定类型的测试
```bash
# 单元测试
pytest test/unit/

# 集成测试
pytest test/integration/

# 性能测试
pytest test/performance/

# 压力测试
pytest test/stress/
```

### 生成测试报告
```bash
pytest test/ --html=test_report.html --cov=. --cov-report=html
```

## 🧪 测试类型

### 单元测试 (Unit Tests)
- **目标**: 测试单个函数或类的功能
- **范围**: 传感器模块、通信模块、工具模块
- **特点**: 快速执行、高覆盖率、独立性强

### 集成测试 (Integration Tests)
- **目标**: 测试模块间的交互和数据流
- **范围**: 传感器到MQTT的完整数据流
- **特点**: 验证系统集成点

### 性能测试 (Performance Tests)
- **目标**: 测试系统性能指标
- **范围**: 数据处理速度、内存使用、响应时间
- **特点**: 量化性能指标

### 压力测试 (Stress Tests)
- **目标**: 测试系统在极限条件下的表现
- **范围**: 高负载、长时间运行、资源耗尽
- **特点**: 发现系统瓶颈

## 📊 测试覆盖率

目标覆盖率：
- **单元测试**: > 90%
- **集成测试**: > 80%
- **整体覆盖率**: > 85%

## 🔍 代码质量

### 静态分析工具
- **pylint**: 代码质量检查
- **flake8**: 代码风格检查
- **mypy**: 类型检查
- **black**: 代码格式化

### 运行代码质量检查
```bash
# 代码质量检查
pylint sensors/ communication/ utils/

# 代码风格检查
flake8 sensors/ communication/ utils/

# 类型检查
mypy sensors/ communication/ utils/
```

## 📝 编写测试

### 单元测试示例
```python
import pytest
from sensors.sensor_collector import SensorDataCollector

class TestSensorDataCollector:
    def test_init(self):
        collector = SensorDataCollector()
        assert collector is not None
    
    def test_get_cth_data(self, mock_sensor):
        collector = SensorDataCollector()
        data = collector.get_cth_data()
        assert 'co2' in data
        assert 'temperature' in data
        assert 'humidity' in data
```

## 🔗 相关文档

- [HTTP测试接口文档](../testing_interface/README.md)
- [API接口文档](../testing_interface/docs/api_reference.md)
- [测试指南](../testing_interface/docs/testing_guide.md)

---

**版本**: 1.0.0  
**更新时间**: 2025-07-30  
**维护者**: IoT传感器监控系统开发团队
