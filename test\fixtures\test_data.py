"""
测试数据生成器

提供各种测试场景所需的数据，包括正常数据、异常数据、边界数据等。
"""

import time
import random
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional


class TestDataGenerator:
    """测试数据生成器"""
    
    @staticmethod
    def generate_sensor_data(
        timestamp: Optional[int] = None,
        temperature: Optional[float] = None,
        humidity: Optional[float] = None,
        co2: Optional[int] = None,
        latitude: Optional[float] = None,
        longitude: Optional[float] = None,
        wind_speed: Optional[float] = None
    ) -> Dict[str, Any]:
        """生成传感器数据"""
        if timestamp is None:
            timestamp = int(time.time())
            
        return {
            "timestamp": timestamp,
            "datetime": datetime.fromtimestamp(timestamp).strftime("%Y-%m-%d %H:%M:%S"),
            "temperature": temperature if temperature is not None else round(random.uniform(20.0, 30.0), 1),
            "humidity": humidity if humidity is not None else round(random.uniform(40.0, 80.0), 1),
            "co2": co2 if co2 is not None else random.randint(300, 1000),
            "latitude": latitude if latitude is not None else round(random.uniform(39.9, 40.0), 6),
            "longitude": longitude if longitude is not None else round(random.uniform(116.4, 116.5), 6),
            "wind_speed": wind_speed if wind_speed is not None else round(random.uniform(0.0, 15.0), 1)
        }
    
    @staticmethod
    def generate_error_data(
        timestamp: Optional[int] = None,
        sensor_type: str = "cth",
        error_type: str = "通信错误",
        error_message: str = "传感器无响应"
    ) -> Dict[str, Any]:
        """生成错误数据"""
        if timestamp is None:
            timestamp = int(time.time())
            
        return {
            "timestamp": timestamp,
            "datetime": datetime.fromtimestamp(timestamp).strftime("%Y-%m-%d %H:%M:%S"),
            "sensor_type": sensor_type,
            "error_type": error_type,
            "error_message": error_message,
            "retry_count": random.randint(1, 5)
        }
    
    @staticmethod
    def generate_co2_status_data(
        timestamp: Optional[int] = None,
        status: str = "normal",
        concentration: Optional[int] = None
    ) -> Dict[str, Any]:
        """生成CO2状态数据"""
        if timestamp is None:
            timestamp = int(time.time())
            
        return {
            "time": timestamp,
            "datetime": datetime.fromtimestamp(timestamp).strftime("%Y-%m-%d %H:%M:%S"),
            "status": status,
            "concentration": concentration if concentration is not None else random.randint(300, 1000),
            "control_action": "none" if status == "normal" else "ventilation"
        }
    
    @staticmethod
    def generate_device_check_data(
        timestamp: Optional[int] = None,
        device_id: str = "TEST_DEVICE_001"
    ) -> Dict[str, Any]:
        """生成设备检查数据"""
        if timestamp is None:
            timestamp = int(time.time())
            
        return {
            "time": timestamp,
            "devid": device_id,
            "ver": "2.0",
            "dir": "up",
            "co2": random.choice([1, 2]),  # 1: 正常, 2: 异常
            "temp": random.choice([1, 2]),
            "hum": random.choice([1, 2]),
            "gps": random.choice([1, 2]),
            "ws": random.choice([1, 2]),
            "camera": random.choice([1, 2]),
            "fan": random.choice([1, 2]),
            "co2device": random.choice([1, 2]),
            "cpu": random.randint(10, 90),
            "memory": random.randint(10, 90),
            "disk": random.randint(10, 90),
            "gpu": random.randint(10, 90)
        }
    
    @staticmethod
    def generate_mosquito_data(
        timestamp: Optional[int] = None,
        device_id: str = "TEST_DEVICE_001",
        detection_count: int = 1
    ) -> Dict[str, Any]:
        """生成蚊子检测数据"""
        if timestamp is None:
            timestamp = int(time.time())
            
        mosquito_types = [
            "Aedes albopictus",
            "Anopheles sinensis", 
            "Culex pipiens",
            "Aedes aegypti",
            "Culex tritaeniorhynchus"
        ]
        
        detections = []
        for i in range(detection_count):
            detection = {
                "count": 1,
                "type": random.choice(mosquito_types),
                "order": i + 1,
                "idnt": random.randint(75, 99),
                "coords": f"{random.randint(100, 1000)},{random.randint(100, 1000)},{random.randint(1100, 2000)},{random.randint(1100, 2000)}",
                "track_id": f"det_{i}_{datetime.fromtimestamp(timestamp).strftime('%Y%m%d_%H%M%S')}"
            }
            detections.append(detection)
        
        return {
            "devid": device_id,
            "ver": "2.0",
            "data": {
                "time": timestamp,
                "picurl": f"https://imgdev.wlwise.com/Mosquito-borne/{datetime.fromtimestamp(timestamp).strftime('%Y%m%d%H%M%S')}_test.jpg",
                "lr": detections
            }
        }
    
    @staticmethod
    def generate_batch_sensor_data(count: int, start_time: Optional[int] = None, interval: int = 60) -> List[Dict[str, Any]]:
        """生成批量传感器数据"""
        if start_time is None:
            start_time = int(time.time()) - (count * interval)
            
        data_list = []
        for i in range(count):
            timestamp = start_time + (i * interval)
            data = TestDataGenerator.generate_sensor_data(timestamp=timestamp)
            data_list.append(data)
            
        return data_list
    
    @staticmethod
    def generate_invalid_sensor_data() -> Dict[str, Any]:
        """生成无效的传感器数据（用于测试错误处理）"""
        return {
            "timestamp": "invalid_timestamp",  # 无效时间戳
            "temperature": "not_a_number",     # 无效温度
            "humidity": -10,                   # 无效湿度（负数）
            "co2": 100000,                     # 无效CO2（超出范围）
            "latitude": 200,                   # 无效纬度
            "longitude": 400,                  # 无效经度
            "wind_speed": -5                   # 无效风速（负数）
        }
    
    @staticmethod
    def generate_boundary_sensor_data() -> List[Dict[str, Any]]:
        """生成边界值传感器数据"""
        boundary_cases = [
            # 最小值
            {
                "timestamp": 0,
                "temperature": -50,
                "humidity": 0,
                "co2": 0,
                "latitude": -90,
                "longitude": -180,
                "wind_speed": 0
            },
            # 最大值
            {
                "timestamp": 2147483647,  # 32位时间戳最大值
                "temperature": 100,
                "humidity": 100,
                "co2": 50000,
                "latitude": 90,
                "longitude": 180,
                "wind_speed": 60
            },
            # 零值
            {
                "timestamp": int(time.time()),
                "temperature": 0,
                "humidity": 0,
                "co2": 0,
                "latitude": 0,
                "longitude": 0,
                "wind_speed": 0
            }
        ]
        
        # 为每个边界案例添加datetime字段
        for case in boundary_cases:
            if case["timestamp"] > 0:
                case["datetime"] = datetime.fromtimestamp(case["timestamp"]).strftime("%Y-%m-%d %H:%M:%S")
            else:
                case["datetime"] = "1970-01-01 00:00:00"
                
        return boundary_cases


# 预定义的测试数据集
SAMPLE_SENSOR_DATA = TestDataGenerator.generate_sensor_data()
SAMPLE_ERROR_DATA = TestDataGenerator.generate_error_data()
SAMPLE_CO2_STATUS_DATA = TestDataGenerator.generate_co2_status_data()
SAMPLE_DEVICE_CHECK_DATA = TestDataGenerator.generate_device_check_data()
SAMPLE_MOSQUITO_DATA = TestDataGenerator.generate_mosquito_data()

# 批量测试数据
BATCH_SENSOR_DATA_10 = TestDataGenerator.generate_batch_sensor_data(10)
BATCH_SENSOR_DATA_100 = TestDataGenerator.generate_batch_sensor_data(100)

# 边界和异常数据
INVALID_SENSOR_DATA = TestDataGenerator.generate_invalid_sensor_data()
BOUNDARY_SENSOR_DATA = TestDataGenerator.generate_boundary_sensor_data()
