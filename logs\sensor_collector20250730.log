2025-07-30 11:14:03,850 - sensor_collector - INFO - 初始化传感器收集器...
2025-07-30 11:14:03,850 - sensor_collector - INFO - 🧪 传感器收集器运行在测试模式
2025-07-30 11:14:03,850 - sensor_collector - INFO - 🧪 启用模拟硬件组件
2025-07-30 11:14:03,850 - sensor_collector - INFO - 串口配置: 端口=/dev/mock_ttyTHS1, 波特率=4800
2025-07-30 11:14:03,850 - sensor_collector - INFO - 🧪 测试模式数据目录: /home/<USER>/main_test/test_data/sensors
2025-07-30 11:14:03,851 - sensor_collector - INFO - 创建目录: /home/<USER>/main_test/test_data/sensors/history
2025-07-30 11:14:03,851 - sensor_collector - INFO - 创建文件: /home/<USER>/main_test/test_data/sensors/history/sensor_data_archive.json
2025-07-30 11:14:03,851 - sensor_collector - INFO - 创建文件: /home/<USER>/main_test/test_data/sensors/history/sensor_errors_archive.json
2025-07-30 11:14:03,851 - sensor_collector - INFO - 当前运行目录: /home/<USER>/main_test
2025-07-30 11:14:03,852 - sensor_collector - INFO - 项目根目录: /home/<USER>/main_test
2025-07-30 11:14:03,852 - sensor_collector - INFO - 数据目录: /home/<USER>/main_test/test_data/sensors
2025-07-30 11:14:03,852 - sensor_collector - INFO - 数据目录写入权限测试成功
2025-07-30 11:14:03,857 - sensor_collector - INFO - 🧪 模拟硬件控制器初始化成功
2025-07-30 11:14:03,857 - sensor_collector - INFO - 🧪 测试模式：跳过真实串口初始化
2025-07-30 11:14:03,861 - sensor_collector - INFO - 已注册内存监控回调
2025-07-30 11:14:03,878 - sensor_collector - INFO - 异步文件I/O初始化完成
2025-07-30 11:14:03,890 - sensor_collector - INFO - 线程管理器初始化完成
2025-07-30 11:14:03,930 - sensor_collector - INFO - 线程管理器初始化完成
2025-07-30 11:14:03,931 - sensor_collector - INFO - [采集线程] 开始传感器数据采集循环(采集间隔:60秒), 线程ID=281472946532640
2025-07-30 11:14:03,931 - sensor_collector - INFO - 传感器数据采集任务已提交到线程管理器
2025-07-30 11:14:03,931 - sensor_collector - INFO - --- 开始传感器数据采集周期 ---
2025-07-30 11:14:03,932 - sensor_collector - INFO - 开始采集传感器数据: 2025-07-30 11:14:03
2025-07-30 11:14:03,932 - sensor_collector - INFO - 开始采集温湿度CO2数据...
2025-07-30 11:14:03,932 - sensor_collector - INFO - cth传感器状态变化: 断开连接 → 正常
2025-07-30 11:14:03,932 - sensor_collector - INFO - 温湿度CO2数据采集成功
2025-07-30 11:14:03,932 - sensor_collector - INFO - 开始采集GPS数据...
2025-07-30 11:14:03,933 - sensor_collector - INFO - gps传感器状态变化: 断开连接 → 正常
2025-07-30 11:14:03,933 - sensor_collector - INFO - GPS数据采集成功
2025-07-30 11:14:03,933 - sensor_collector - INFO - 开始采集风速数据...
2025-07-30 11:14:03,933 - sensor_collector - INFO - ws传感器状态变化: 断开连接 → 正常
2025-07-30 11:14:03,933 - sensor_collector - INFO - 风速数据采集成功
2025-07-30 11:14:03,933 - sensor_collector - INFO - 成功采集到 6 项传感器数据
2025-07-30 11:14:03,933 - sensor_collector - INFO - 完成数据质量验证
2025-07-30 11:14:03,934 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753845243, 缓存大小: 1/1000
2025-07-30 11:14:33,960 - sensor_collector - INFO - 已将 1 条传感器数据提交到异步写入队列
2025-07-30 11:15:03,999 - sensor_collector - INFO - --- 开始传感器数据采集周期 ---
2025-07-30 11:15:03,999 - sensor_collector - INFO - 开始采集传感器数据: 2025-07-30 11:15:03
2025-07-30 11:15:04,000 - sensor_collector - INFO - 开始采集温湿度CO2数据...
2025-07-30 11:15:04,000 - sensor_collector - INFO - 温湿度CO2数据采集成功
2025-07-30 11:15:04,000 - sensor_collector - INFO - 开始采集GPS数据...
2025-07-30 11:15:04,000 - sensor_collector - INFO - GPS数据采集成功
2025-07-30 11:15:04,000 - sensor_collector - INFO - 开始采集风速数据...
2025-07-30 11:15:04,001 - sensor_collector - INFO - 风速数据采集成功
2025-07-30 11:15:04,001 - sensor_collector - INFO - 成功采集到 6 项传感器数据
2025-07-30 11:15:04,001 - sensor_collector - INFO - 完成数据质量验证
2025-07-30 11:15:04,002 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753845303, 缓存大小: 1/1000
2025-07-30 11:15:33,978 - sensor_collector - INFO - 已将 1 条传感器数据提交到异步写入队列
2025-07-30 11:16:04,069 - sensor_collector - INFO - --- 开始传感器数据采集周期 ---
2025-07-30 11:16:04,070 - sensor_collector - INFO - 开始采集传感器数据: 2025-07-30 11:16:04
2025-07-30 11:16:04,070 - sensor_collector - INFO - 开始采集温湿度CO2数据...
2025-07-30 11:16:04,070 - sensor_collector - INFO - 温湿度CO2数据采集成功
2025-07-30 11:16:04,071 - sensor_collector - INFO - 开始采集GPS数据...
2025-07-30 11:16:04,071 - sensor_collector - INFO - GPS数据采集成功
2025-07-30 11:16:04,071 - sensor_collector - INFO - 开始采集风速数据...
2025-07-30 11:16:04,071 - sensor_collector - INFO - 风速数据采集成功
2025-07-30 11:16:04,071 - sensor_collector - INFO - 成功采集到 6 项传感器数据
2025-07-30 11:16:04,072 - sensor_collector - INFO - 完成数据质量验证
2025-07-30 11:16:04,072 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753845364, 缓存大小: 1/1000
2025-07-30 11:16:34,019 - sensor_collector - INFO - 已将 1 条传感器数据提交到异步写入队列
2025-07-30 11:17:04,137 - sensor_collector - INFO - --- 开始传感器数据采集周期 ---
2025-07-30 11:17:04,138 - sensor_collector - INFO - 开始采集传感器数据: 2025-07-30 11:17:04
2025-07-30 11:17:04,138 - sensor_collector - INFO - 开始采集温湿度CO2数据...
2025-07-30 11:17:04,138 - sensor_collector - INFO - 温湿度CO2数据采集成功
2025-07-30 11:17:04,139 - sensor_collector - INFO - 开始采集GPS数据...
2025-07-30 11:17:04,139 - sensor_collector - INFO - GPS数据采集成功
2025-07-30 11:17:04,139 - sensor_collector - INFO - 开始采集风速数据...
2025-07-30 11:17:04,139 - sensor_collector - INFO - 风速数据采集成功
2025-07-30 11:17:04,140 - sensor_collector - INFO - 成功采集到 6 项传感器数据
2025-07-30 11:17:04,140 - sensor_collector - INFO - 完成数据质量验证
2025-07-30 11:17:04,140 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753845424, 缓存大小: 1/1000
2025-07-30 11:17:34,079 - sensor_collector - INFO - 已将 1 条传感器数据提交到异步写入队列
2025-07-30 11:18:04,207 - sensor_collector - INFO - --- 开始传感器数据采集周期 ---
2025-07-30 11:18:04,208 - sensor_collector - INFO - 开始采集传感器数据: 2025-07-30 11:18:04
2025-07-30 11:18:04,208 - sensor_collector - INFO - 开始采集温湿度CO2数据...
2025-07-30 11:18:04,208 - sensor_collector - INFO - 温湿度CO2数据采集成功
2025-07-30 11:18:04,208 - sensor_collector - INFO - 开始采集GPS数据...
2025-07-30 11:18:04,209 - sensor_collector - INFO - GPS数据采集成功
2025-07-30 11:18:04,209 - sensor_collector - INFO - 开始采集风速数据...
2025-07-30 11:18:04,209 - sensor_collector - INFO - 风速数据采集成功
2025-07-30 11:18:04,209 - sensor_collector - INFO - 成功采集到 6 项传感器数据
2025-07-30 11:18:04,209 - sensor_collector - INFO - 完成数据质量验证
2025-07-30 11:18:04,210 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753845484, 缓存大小: 1/1000
2025-07-30 11:18:34,140 - sensor_collector - INFO - 已将 1 条传感器数据提交到异步写入队列
2025-07-30 11:19:04,275 - sensor_collector - INFO - --- 开始传感器数据采集周期 ---
2025-07-30 11:19:04,275 - sensor_collector - INFO - 开始采集传感器数据: 2025-07-30 11:19:04
2025-07-30 11:19:04,276 - sensor_collector - INFO - 开始采集温湿度CO2数据...
2025-07-30 11:19:04,276 - sensor_collector - INFO - 温湿度CO2数据采集成功
2025-07-30 11:19:04,276 - sensor_collector - INFO - 开始采集GPS数据...
2025-07-30 11:19:04,276 - sensor_collector - INFO - GPS数据采集成功
2025-07-30 11:19:04,277 - sensor_collector - INFO - 开始采集风速数据...
2025-07-30 11:19:04,277 - sensor_collector - INFO - 风速数据采集成功
2025-07-30 11:19:04,277 - sensor_collector - INFO - 成功采集到 6 项传感器数据
2025-07-30 11:19:04,278 - sensor_collector - INFO - 完成数据质量验证
2025-07-30 11:19:04,278 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753845544, 缓存大小: 1/1000
2025-07-30 11:19:34,181 - sensor_collector - INFO - 已将 1 条传感器数据提交到异步写入队列
2025-07-30 11:20:04,344 - sensor_collector - INFO - --- 开始传感器数据采集周期 ---
2025-07-30 11:20:04,345 - sensor_collector - INFO - 开始采集传感器数据: 2025-07-30 11:20:04
2025-07-30 11:20:04,345 - sensor_collector - INFO - 开始采集温湿度CO2数据...
2025-07-30 11:20:04,346 - sensor_collector - INFO - 温湿度CO2数据采集成功
2025-07-30 11:20:04,346 - sensor_collector - INFO - 开始采集GPS数据...
2025-07-30 11:20:04,346 - sensor_collector - INFO - GPS数据采集成功
2025-07-30 11:20:04,346 - sensor_collector - INFO - 开始采集风速数据...
2025-07-30 11:20:04,347 - sensor_collector - INFO - 风速数据采集成功
2025-07-30 11:20:04,347 - sensor_collector - INFO - 成功采集到 6 项传感器数据
2025-07-30 11:20:04,347 - sensor_collector - INFO - 完成数据质量验证
2025-07-30 11:20:04,348 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753845604, 缓存大小: 1/1000
2025-07-30 11:20:34,204 - sensor_collector - INFO - 已将 1 条传感器数据提交到异步写入队列
2025-07-30 11:21:04,411 - sensor_collector - INFO - --- 开始传感器数据采集周期 ---
2025-07-30 11:21:04,412 - sensor_collector - INFO - 开始采集传感器数据: 2025-07-30 11:21:04
2025-07-30 11:21:04,412 - sensor_collector - INFO - 开始采集温湿度CO2数据...
2025-07-30 11:21:04,412 - sensor_collector - INFO - 温湿度CO2数据采集成功
2025-07-30 11:21:04,413 - sensor_collector - INFO - 开始采集GPS数据...
2025-07-30 11:21:04,413 - sensor_collector - INFO - GPS数据采集成功
2025-07-30 11:21:04,413 - sensor_collector - INFO - 开始采集风速数据...
2025-07-30 11:21:04,413 - sensor_collector - INFO - 风速数据采集成功
2025-07-30 11:21:04,414 - sensor_collector - INFO - 成功采集到 6 项传感器数据
2025-07-30 11:21:04,414 - sensor_collector - INFO - 完成数据质量验证
2025-07-30 11:21:04,414 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753845664, 缓存大小: 1/1000
2025-07-30 11:21:32,949 - sensor_collector - INFO - 
正在停止传感器数据采集线程...
2025-07-30 11:21:32,949 - sensor_collector - INFO - 已取消传感器数据采集任务
2025-07-30 11:21:32,950 - sensor_collector - INFO - 已取消传感器缓存刷新定期任务
2025-07-30 11:21:32,950 - sensor_collector - INFO - 执行停止前的缓存刷新...
2025-07-30 11:21:32,950 - sensor_collector - INFO - 已将 1 条传感器数据提交到异步写入队列
2025-07-30 11:21:32,950 - sensor_collector - INFO - 执行停止前的数据强制备份...
2025-07-30 11:21:32,953 - sensor_collector - INFO - 已创建数据备份: /home/<USER>/main_test/test_data/sensors/current/sensor_data.json.20250730112132.bak
2025-07-30 11:21:33,435 - sensor_collector - INFO - 🧪 模拟硬件控制器已停止
2025-07-30 11:21:33,436 - sensor_collector - INFO - 传感器数据采集线程已停止
2025-07-30 11:39:56,636 - sensor_collector - INFO - 初始化传感器收集器...
2025-07-30 11:39:56,636 - sensor_collector - INFO - 🧪 传感器收集器运行在测试模式
2025-07-30 11:39:56,636 - sensor_collector - INFO - 🧪 启用模拟硬件组件
2025-07-30 11:39:56,636 - sensor_collector - INFO - 串口配置: 端口=/dev/mock_ttyTHS1, 波特率=4800
2025-07-30 11:39:56,636 - sensor_collector - INFO - 🧪 测试模式数据目录: /home/<USER>/main_test/test_data/sensors
2025-07-30 11:39:56,636 - sensor_collector - INFO - 当前运行目录: /home/<USER>/main_test
2025-07-30 11:39:56,636 - sensor_collector - INFO - 项目根目录: /home/<USER>/main_test
2025-07-30 11:39:56,637 - sensor_collector - INFO - 数据目录: /home/<USER>/main_test/test_data/sensors
2025-07-30 11:39:56,637 - sensor_collector - INFO - 数据目录写入权限测试成功
2025-07-30 11:39:56,642 - sensor_collector - INFO - 🧪 模拟硬件控制器初始化成功
2025-07-30 11:39:56,642 - sensor_collector - INFO - 🧪 测试模式：跳过真实串口初始化
2025-07-30 11:39:56,646 - sensor_collector - INFO - 已注册内存监控回调
2025-07-30 11:39:56,663 - sensor_collector - INFO - 异步文件I/O初始化完成
2025-07-30 11:39:56,675 - sensor_collector - INFO - 线程管理器初始化完成
2025-07-30 11:39:56,717 - sensor_collector - INFO - 线程管理器初始化完成
2025-07-30 11:39:56,718 - sensor_collector - INFO - [采集线程] 开始传感器数据采集循环(采集间隔:60秒), 线程ID=281472946532640
2025-07-30 11:39:56,718 - sensor_collector - INFO - 传感器数据采集任务已提交到线程管理器
2025-07-30 11:39:56,718 - sensor_collector - INFO - --- 开始传感器数据采集周期 ---
2025-07-30 11:39:56,719 - sensor_collector - INFO - 开始采集传感器数据: 2025-07-30 11:39:56
2025-07-30 11:39:56,719 - sensor_collector - INFO - 开始采集温湿度CO2数据...
2025-07-30 11:39:56,719 - sensor_collector - INFO - cth传感器状态变化: 断开连接 → 正常
2025-07-30 11:39:56,719 - sensor_collector - INFO - 温湿度CO2数据采集成功
2025-07-30 11:39:56,720 - sensor_collector - INFO - 开始采集GPS数据...
2025-07-30 11:39:56,720 - sensor_collector - INFO - gps传感器状态变化: 断开连接 → 正常
2025-07-30 11:39:56,720 - sensor_collector - INFO - GPS数据采集成功
2025-07-30 11:39:56,720 - sensor_collector - INFO - 开始采集风速数据...
2025-07-30 11:39:56,720 - sensor_collector - INFO - ws传感器状态变化: 断开连接 → 正常
2025-07-30 11:39:56,720 - sensor_collector - INFO - 风速数据采集成功
2025-07-30 11:39:56,720 - sensor_collector - INFO - 成功采集到 6 项传感器数据
2025-07-30 11:39:56,720 - sensor_collector - INFO - 完成数据质量验证
2025-07-30 11:39:56,720 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753846796, 缓存大小: 1/1000
2025-07-30 11:40:26,742 - sensor_collector - INFO - 已将 1 条传感器数据提交到异步写入队列
2025-07-30 11:40:56,780 - sensor_collector - INFO - --- 开始传感器数据采集周期 ---
2025-07-30 11:40:56,780 - sensor_collector - INFO - 开始采集传感器数据: 2025-07-30 11:40:56
2025-07-30 11:40:56,781 - sensor_collector - INFO - 开始采集温湿度CO2数据...
2025-07-30 11:40:56,781 - sensor_collector - INFO - 温湿度CO2数据采集成功
2025-07-30 11:40:56,781 - sensor_collector - INFO - 开始采集GPS数据...
2025-07-30 11:40:56,781 - sensor_collector - INFO - GPS数据采集成功
2025-07-30 11:40:56,781 - sensor_collector - INFO - 开始采集风速数据...
2025-07-30 11:40:56,781 - sensor_collector - INFO - 风速数据采集成功
2025-07-30 11:40:56,781 - sensor_collector - INFO - 成功采集到 6 项传感器数据
2025-07-30 11:40:56,781 - sensor_collector - INFO - 完成数据质量验证
2025-07-30 11:40:56,782 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753846856, 缓存大小: 1/1000
2025-07-30 11:41:22,299 - sensor_collector - INFO - 
正在停止传感器数据采集线程...
2025-07-30 11:41:22,299 - sensor_collector - INFO - 已取消传感器数据采集任务
2025-07-30 11:41:22,299 - sensor_collector - INFO - 已取消传感器缓存刷新定期任务
2025-07-30 11:41:22,300 - sensor_collector - INFO - 执行停止前的缓存刷新...
2025-07-30 11:41:22,300 - sensor_collector - INFO - 已将 1 条传感器数据提交到异步写入队列
2025-07-30 11:41:22,300 - sensor_collector - INFO - 执行停止前的数据强制备份...
2025-07-30 11:41:22,302 - sensor_collector - INFO - 已创建数据备份: /home/<USER>/main_test/test_data/sensors/current/sensor_data.json.20250730114122.bak
2025-07-30 11:41:22,736 - sensor_collector - INFO - 🧪 模拟硬件控制器已停止
2025-07-30 11:41:22,736 - sensor_collector - INFO - 传感器数据采集线程已停止
2025-07-30 12:02:55,305 - sensor_collector - INFO - 初始化传感器收集器...
2025-07-30 12:02:55,305 - sensor_collector - INFO - 🧪 传感器收集器运行在测试模式
2025-07-30 12:02:55,305 - sensor_collector - INFO - 🧪 启用模拟硬件组件
2025-07-30 12:02:55,306 - sensor_collector - INFO - 串口配置: 端口=/dev/mock_ttyTHS1, 波特率=4800
2025-07-30 12:02:55,306 - sensor_collector - INFO - 🧪 测试模式数据目录: /home/<USER>/main_test/test_data/sensors
2025-07-30 12:02:55,306 - sensor_collector - INFO - 当前运行目录: /home/<USER>/main_test
2025-07-30 12:02:55,306 - sensor_collector - INFO - 项目根目录: /home/<USER>/main_test
2025-07-30 12:02:55,306 - sensor_collector - INFO - 数据目录: /home/<USER>/main_test/test_data/sensors
2025-07-30 12:02:55,306 - sensor_collector - INFO - 数据目录写入权限测试成功
2025-07-30 12:02:55,311 - sensor_collector - INFO - 🧪 模拟硬件控制器初始化成功
2025-07-30 12:02:55,312 - sensor_collector - INFO - 🧪 测试模式：跳过真实串口初始化
2025-07-30 12:02:55,315 - sensor_collector - INFO - 已注册内存监控回调
2025-07-30 12:02:55,334 - sensor_collector - INFO - 异步文件I/O初始化完成
2025-07-30 12:02:55,346 - sensor_collector - INFO - 线程管理器初始化完成
2025-07-30 12:02:55,357 - sensor_collector - INFO - 线程管理器初始化完成
2025-07-30 12:02:55,358 - sensor_collector - INFO - [采集线程] 开始传感器数据采集循环(采集间隔:60秒), 线程ID=281473273622816
2025-07-30 12:02:55,358 - sensor_collector - INFO - 传感器数据采集任务已提交到线程管理器
2025-07-30 12:02:55,358 - sensor_collector - INFO - --- 开始传感器数据采集周期 ---
2025-07-30 12:02:55,359 - sensor_collector - INFO - 开始采集传感器数据: 2025-07-30 12:02:55
2025-07-30 12:02:55,359 - sensor_collector - INFO - 开始采集温湿度CO2数据...
2025-07-30 12:02:55,359 - sensor_collector - INFO - cth传感器状态变化: 断开连接 → 正常
2025-07-30 12:02:55,359 - sensor_collector - INFO - 温湿度CO2数据采集成功
2025-07-30 12:02:55,359 - sensor_collector - INFO - 开始采集GPS数据...
2025-07-30 12:02:55,360 - sensor_collector - INFO - gps传感器状态变化: 断开连接 → 正常
2025-07-30 12:02:55,360 - sensor_collector - INFO - GPS数据采集成功
2025-07-30 12:02:55,360 - sensor_collector - INFO - 开始采集风速数据...
2025-07-30 12:02:55,360 - sensor_collector - INFO - ws传感器状态变化: 断开连接 → 正常
2025-07-30 12:02:55,360 - sensor_collector - INFO - 风速数据采集成功
2025-07-30 12:02:55,360 - sensor_collector - INFO - 成功采集到 6 项传感器数据
2025-07-30 12:02:55,360 - sensor_collector - INFO - 完成数据质量验证
2025-07-30 12:02:55,360 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753848175, 缓存大小: 1/1000
2025-07-30 12:03:25,387 - sensor_collector - INFO - 已将 1 条传感器数据提交到异步写入队列
2025-07-30 12:03:39,814 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848219, 'datetime': '2025-07-30 12:03:39', 'co2': 450}
2025-07-30 12:03:39,815 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753848219, 缓存大小: 1/1000
2025-07-30 12:03:39,815 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:03:46,272 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848226, 'datetime': '2025-07-30 12:03:46', 'temperature': 25.5}
2025-07-30 12:03:46,272 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753848226, 缓存大小: 2/1000
2025-07-30 12:03:46,272 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:03:53,578 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848233, 'datetime': '2025-07-30 12:03:53', 'humidity': 65.2}
2025-07-30 12:03:53,578 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753848233, 缓存大小: 3/1000
2025-07-30 12:03:53,578 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:03:55,420 - sensor_collector - INFO - --- 开始传感器数据采集周期 ---
2025-07-30 12:03:55,420 - sensor_collector - INFO - 开始采集传感器数据: 2025-07-30 12:03:55
2025-07-30 12:03:55,421 - sensor_collector - INFO - 开始采集温湿度CO2数据...
2025-07-30 12:03:55,421 - sensor_collector - INFO - 温湿度CO2数据采集成功
2025-07-30 12:03:55,421 - sensor_collector - INFO - 开始采集GPS数据...
2025-07-30 12:03:55,421 - sensor_collector - INFO - GPS数据采集成功
2025-07-30 12:03:55,421 - sensor_collector - INFO - 开始采集风速数据...
2025-07-30 12:03:55,422 - sensor_collector - INFO - 风速数据采集成功
2025-07-30 12:03:55,422 - sensor_collector - INFO - 成功采集到 6 项传感器数据
2025-07-30 12:03:55,422 - sensor_collector - INFO - 完成数据质量验证
2025-07-30 12:03:55,422 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753848235, 缓存大小: 4/1000
2025-07-30 12:04:25,442 - sensor_collector - INFO - 已将 4 条传感器数据提交到异步写入队列
2025-07-30 12:04:55,482 - sensor_collector - INFO - --- 开始传感器数据采集周期 ---
2025-07-30 12:04:55,482 - sensor_collector - INFO - 开始采集传感器数据: 2025-07-30 12:04:55
2025-07-30 12:04:55,482 - sensor_collector - INFO - 开始采集温湿度CO2数据...
2025-07-30 12:04:55,483 - sensor_collector - INFO - 温湿度CO2数据采集成功
2025-07-30 12:04:55,483 - sensor_collector - INFO - 开始采集GPS数据...
2025-07-30 12:04:55,483 - sensor_collector - INFO - GPS数据采集成功
2025-07-30 12:04:55,483 - sensor_collector - INFO - 开始采集风速数据...
2025-07-30 12:04:55,483 - sensor_collector - INFO - 风速数据采集成功
2025-07-30 12:04:55,483 - sensor_collector - INFO - 成功采集到 6 项传感器数据
2025-07-30 12:04:55,483 - sensor_collector - INFO - 完成数据质量验证
2025-07-30 12:04:55,484 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753848295, 缓存大小: 1/1000
2025-07-30 12:04:55,727 - sensor_collector - INFO - 
正在停止传感器数据采集线程...
2025-07-30 12:04:55,727 - sensor_collector - INFO - 已取消传感器数据采集任务
2025-07-30 12:04:55,728 - sensor_collector - INFO - 已取消传感器缓存刷新定期任务
2025-07-30 12:04:55,728 - sensor_collector - INFO - 执行停止前的缓存刷新...
2025-07-30 12:04:55,728 - sensor_collector - INFO - 已将 1 条传感器数据提交到异步写入队列
2025-07-30 12:04:55,729 - sensor_collector - INFO - 执行停止前的数据强制备份...
2025-07-30 12:04:55,732 - sensor_collector - INFO - 已创建数据备份: /home/<USER>/main_test/test_data/sensors/current/sensor_data.json.20250730120455.bak
2025-07-30 12:04:56,438 - sensor_collector - INFO - 🧪 模拟硬件控制器已停止
2025-07-30 12:04:56,438 - sensor_collector - INFO - 传感器数据采集线程已停止
2025-07-30 12:10:03,825 - sensor_collector - INFO - 初始化传感器收集器...
2025-07-30 12:10:03,825 - sensor_collector - INFO - 🧪 传感器收集器运行在测试模式
2025-07-30 12:10:03,825 - sensor_collector - INFO - 🧪 启用模拟硬件组件
2025-07-30 12:10:03,825 - sensor_collector - INFO - 串口配置: 端口=/dev/mock_ttyTHS1, 波特率=4800
2025-07-30 12:10:03,825 - sensor_collector - INFO - 🧪 测试模式数据目录: /home/<USER>/main_test/test_data/sensors
2025-07-30 12:10:03,826 - sensor_collector - INFO - 当前运行目录: /home/<USER>/main_test
2025-07-30 12:10:03,826 - sensor_collector - INFO - 项目根目录: /home/<USER>/main_test
2025-07-30 12:10:03,826 - sensor_collector - INFO - 数据目录: /home/<USER>/main_test/test_data/sensors
2025-07-30 12:10:03,826 - sensor_collector - INFO - 数据目录写入权限测试成功
2025-07-30 12:10:03,827 - sensor_collector - INFO - 🧪 模拟硬件控制器初始化成功
2025-07-30 12:10:03,828 - sensor_collector - INFO - 🧪 测试模式：跳过真实串口初始化
2025-07-30 12:10:03,829 - sensor_collector - INFO - 已注册内存监控回调
2025-07-30 12:10:03,842 - sensor_collector - INFO - 异步文件I/O初始化完成
2025-07-30 12:10:03,849 - sensor_collector - INFO - 线程管理器初始化完成
2025-07-30 12:10:03,855 - sensor_collector - INFO - 线程管理器初始化完成
2025-07-30 12:10:03,856 - sensor_collector - INFO - [采集线程] 开始传感器数据采集循环(采集间隔:60秒), 线程ID=281473223422240
2025-07-30 12:10:03,856 - sensor_collector - INFO - 传感器数据采集任务已提交到线程管理器
2025-07-30 12:10:03,856 - sensor_collector - INFO - --- 开始传感器数据采集周期 ---
2025-07-30 12:10:03,856 - sensor_collector - INFO - 开始采集传感器数据: 2025-07-30 12:10:03
2025-07-30 12:10:03,857 - sensor_collector - INFO - 开始采集温湿度CO2数据...
2025-07-30 12:10:03,857 - sensor_collector - INFO - cth传感器状态变化: 断开连接 → 正常
2025-07-30 12:10:03,857 - sensor_collector - INFO - 温湿度CO2数据采集成功
2025-07-30 12:10:03,857 - sensor_collector - INFO - 开始采集GPS数据...
2025-07-30 12:10:03,857 - sensor_collector - INFO - gps传感器状态变化: 断开连接 → 正常
2025-07-30 12:10:03,857 - sensor_collector - INFO - GPS数据采集成功
2025-07-30 12:10:03,857 - sensor_collector - INFO - 开始采集风速数据...
2025-07-30 12:10:03,857 - sensor_collector - INFO - ws传感器状态变化: 断开连接 → 正常
2025-07-30 12:10:03,857 - sensor_collector - INFO - 风速数据采集成功
2025-07-30 12:10:03,858 - sensor_collector - INFO - 成功采集到 6 项传感器数据
2025-07-30 12:10:03,858 - sensor_collector - INFO - 完成数据质量验证
2025-07-30 12:10:03,858 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753848603, 缓存大小: 1/1000
2025-07-30 12:10:33,883 - sensor_collector - INFO - 已将 1 条传感器数据提交到异步写入队列
2025-07-30 12:11:03,917 - sensor_collector - INFO - --- 开始传感器数据采集周期 ---
2025-07-30 12:11:03,918 - sensor_collector - INFO - 开始采集传感器数据: 2025-07-30 12:11:03
2025-07-30 12:11:03,918 - sensor_collector - INFO - 开始采集温湿度CO2数据...
2025-07-30 12:11:03,918 - sensor_collector - INFO - 温湿度CO2数据采集成功
2025-07-30 12:11:03,918 - sensor_collector - INFO - 开始采集GPS数据...
2025-07-30 12:11:03,918 - sensor_collector - INFO - GPS数据采集成功
2025-07-30 12:11:03,918 - sensor_collector - INFO - 开始采集风速数据...
2025-07-30 12:11:03,919 - sensor_collector - INFO - 风速数据采集成功
2025-07-30 12:11:03,919 - sensor_collector - INFO - 成功采集到 6 项传感器数据
2025-07-30 12:11:03,919 - sensor_collector - INFO - 完成数据质量验证
2025-07-30 12:11:03,919 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753848663, 缓存大小: 1/1000
2025-07-30 12:11:33,934 - sensor_collector - INFO - 已将 1 条传感器数据提交到异步写入队列
2025-07-30 12:12:03,979 - sensor_collector - INFO - --- 开始传感器数据采集周期 ---
2025-07-30 12:12:03,979 - sensor_collector - INFO - 开始采集传感器数据: 2025-07-30 12:12:03
2025-07-30 12:12:03,980 - sensor_collector - INFO - 开始采集温湿度CO2数据...
2025-07-30 12:12:03,980 - sensor_collector - INFO - 温湿度CO2数据采集成功
2025-07-30 12:12:03,980 - sensor_collector - INFO - 开始采集GPS数据...
2025-07-30 12:12:03,980 - sensor_collector - INFO - GPS数据采集成功
2025-07-30 12:12:03,980 - sensor_collector - INFO - 开始采集风速数据...
2025-07-30 12:12:03,980 - sensor_collector - INFO - 风速数据采集成功
2025-07-30 12:12:03,980 - sensor_collector - INFO - 成功采集到 6 项传感器数据
2025-07-30 12:12:03,981 - sensor_collector - INFO - 完成数据质量验证
2025-07-30 12:12:03,981 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753848723, 缓存大小: 1/1000
2025-07-30 12:12:33,991 - sensor_collector - INFO - 已将 1 条传感器数据提交到异步写入队列
2025-07-30 12:12:51,309 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848771, 'datetime': '2025-07-30 12:12:51', 'co2': 401, 'temperature': 26, 'humidity': 61}
2025-07-30 12:12:51,310 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753848771, 缓存大小: 1/1000
2025-07-30 12:12:51,310 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:12:52,330 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848772, 'datetime': '2025-07-30 12:12:52', 'co2': 402, 'temperature': 27, 'humidity': 62}
2025-07-30 12:12:52,330 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753848772, 缓存大小: 2/1000
2025-07-30 12:12:52,330 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:12:53,349 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848773, 'datetime': '2025-07-30 12:12:53', 'co2': 403, 'temperature': 28, 'humidity': 63}
2025-07-30 12:12:53,349 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753848773, 缓存大小: 3/1000
2025-07-30 12:12:53,350 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:12:54,369 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848774, 'datetime': '2025-07-30 12:12:54', 'co2': 404, 'temperature': 29, 'humidity': 64}
2025-07-30 12:12:54,370 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753848774, 缓存大小: 4/1000
2025-07-30 12:12:54,370 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:12:55,389 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848775, 'datetime': '2025-07-30 12:12:55', 'co2': 405, 'temperature': 25, 'humidity': 65}
2025-07-30 12:12:55,390 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753848775, 缓存大小: 5/1000
2025-07-30 12:12:55,390 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:12:56,409 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848776, 'datetime': '2025-07-30 12:12:56', 'co2': 406, 'temperature': 26, 'humidity': 66}
2025-07-30 12:12:56,409 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753848776, 缓存大小: 6/1000
2025-07-30 12:12:56,409 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:12:57,429 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848777, 'datetime': '2025-07-30 12:12:57', 'co2': 407, 'temperature': 27, 'humidity': 67}
2025-07-30 12:12:57,429 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753848777, 缓存大小: 7/1000
2025-07-30 12:12:57,430 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:12:58,449 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848778, 'datetime': '2025-07-30 12:12:58', 'co2': 408, 'temperature': 28, 'humidity': 68}
2025-07-30 12:12:58,449 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753848778, 缓存大小: 8/1000
2025-07-30 12:12:58,450 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:12:59,469 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848779, 'datetime': '2025-07-30 12:12:59', 'co2': 409, 'temperature': 29, 'humidity': 69}
2025-07-30 12:12:59,469 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753848779, 缓存大小: 9/1000
2025-07-30 12:12:59,469 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:00,489 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848780, 'datetime': '2025-07-30 12:13:00', 'co2': 410, 'temperature': 25, 'humidity': 60}
2025-07-30 12:13:00,489 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753848780, 缓存大小: 10/1000
2025-07-30 12:13:00,489 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:04,041 - sensor_collector - INFO - --- 开始传感器数据采集周期 ---
2025-07-30 12:13:04,041 - sensor_collector - INFO - 开始采集传感器数据: 2025-07-30 12:13:04
2025-07-30 12:13:04,041 - sensor_collector - INFO - 开始采集温湿度CO2数据...
2025-07-30 12:13:04,042 - sensor_collector - INFO - 温湿度CO2数据采集成功
2025-07-30 12:13:04,042 - sensor_collector - INFO - 开始采集GPS数据...
2025-07-30 12:13:04,042 - sensor_collector - INFO - GPS数据采集成功
2025-07-30 12:13:04,042 - sensor_collector - INFO - 开始采集风速数据...
2025-07-30 12:13:04,042 - sensor_collector - INFO - 风速数据采集成功
2025-07-30 12:13:04,042 - sensor_collector - INFO - 成功采集到 6 项传感器数据
2025-07-30 12:13:04,043 - sensor_collector - INFO - 完成数据质量验证
2025-07-30 12:13:04,043 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753848784, 缓存大小: 11/1000
2025-07-30 12:13:19,347 - sensor_collector - INFO - 接收到测试数据注入: {'co2': 410, 'temperature': 25.1, 'humidity': 61.0}
2025-07-30 12:13:19,348 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753848799, 缓存大小: 12/1000
2025-07-30 12:13:19,348 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:19,348 - sensor_collector - INFO - 接收到测试数据注入: {'co2': 415, 'temperature': 25.3, 'humidity': 62.0}
2025-07-30 12:13:19,348 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:19,349 - sensor_collector - INFO - 接收到测试数据注入: {'co2': 420, 'temperature': 25.5, 'humidity': 63.0}
2025-07-30 12:13:19,349 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:19,349 - sensor_collector - INFO - 接收到测试数据注入: {'co2': 425, 'temperature': 25.7, 'humidity': 64.0}
2025-07-30 12:13:19,349 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:19,349 - sensor_collector - INFO - 接收到测试数据注入: {'co2': 430, 'temperature': 25.9, 'humidity': 65.0}
2025-07-30 12:13:19,349 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:19,350 - sensor_collector - INFO - 接收到测试数据注入: {'co2': 435, 'temperature': 26.1, 'humidity': 66.0}
2025-07-30 12:13:19,350 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:19,350 - sensor_collector - INFO - 接收到测试数据注入: {'co2': 440, 'temperature': 26.3, 'humidity': 67.0}
2025-07-30 12:13:19,350 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:19,350 - sensor_collector - INFO - 接收到测试数据注入: {'co2': 445, 'temperature': 26.5, 'humidity': 68.0}
2025-07-30 12:13:19,350 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:19,350 - sensor_collector - INFO - 接收到测试数据注入: {'co2': 450, 'temperature': 26.7, 'humidity': 69.0}
2025-07-30 12:13:19,351 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:19,351 - sensor_collector - INFO - 接收到测试数据注入: {'co2': 455, 'temperature': 26.9, 'humidity': 70.0}
2025-07-30 12:13:19,351 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:19,351 - sensor_collector - INFO - 接收到测试数据注入: {'co2': 460, 'temperature': 27.1, 'humidity': 71.0}
2025-07-30 12:13:19,351 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:19,351 - sensor_collector - INFO - 接收到测试数据注入: {'co2': 465, 'temperature': 27.3, 'humidity': 72.0}
2025-07-30 12:13:19,351 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:19,351 - sensor_collector - INFO - 接收到测试数据注入: {'co2': 470, 'temperature': 27.5, 'humidity': 73.0}
2025-07-30 12:13:19,351 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:19,352 - sensor_collector - INFO - 接收到测试数据注入: {'co2': 475, 'temperature': 27.7, 'humidity': 74.0}
2025-07-30 12:13:19,352 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:19,352 - sensor_collector - INFO - 接收到测试数据注入: {'co2': 480, 'temperature': 27.9, 'humidity': 75.0}
2025-07-30 12:13:19,352 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:19,352 - sensor_collector - INFO - 接收到测试数据注入: {'co2': 485, 'temperature': 28.1, 'humidity': 76.0}
2025-07-30 12:13:19,352 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:19,352 - sensor_collector - INFO - 接收到测试数据注入: {'co2': 490, 'temperature': 28.3, 'humidity': 77.0}
2025-07-30 12:13:19,352 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:19,352 - sensor_collector - INFO - 接收到测试数据注入: {'co2': 495, 'temperature': 28.5, 'humidity': 78.0}
2025-07-30 12:13:19,353 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:19,353 - sensor_collector - INFO - 接收到测试数据注入: {'co2': 500, 'temperature': 28.7, 'humidity': 79.0}
2025-07-30 12:13:19,353 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:19,353 - sensor_collector - INFO - 接收到测试数据注入: {'co2': 505, 'temperature': 28.9, 'humidity': 80.0}
2025-07-30 12:13:19,353 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:34,046 - sensor_collector - INFO - 已将 12 条传感器数据提交到异步写入队列
2025-07-30 12:13:40,894 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848820, 'datetime': '2025-07-30 12:13:40', 'co2': 568, 'temperature': 24, 'humidity': 60}
2025-07-30 12:13:40,895 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753848820, 缓存大小: 1/1000
2025-07-30 12:13:40,895 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:40,896 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848820, 'datetime': '2025-07-30 12:13:40', 'co2': 594, 'temperature': 33, 'humidity': 79}
2025-07-30 12:13:40,900 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848820, 'datetime': '2025-07-30 12:13:40', 'co2': 423, 'temperature': 20, 'humidity': 68}
2025-07-30 12:13:40,901 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:40,901 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:40,906 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848820, 'datetime': '2025-07-30 12:13:40', 'co2': 522, 'temperature': 34, 'humidity': 52}
2025-07-30 12:13:40,907 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848820, 'datetime': '2025-07-30 12:13:40', 'co2': 520, 'temperature': 30, 'humidity': 53}
2025-07-30 12:13:40,907 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:40,908 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:40,909 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848820, 'datetime': '2025-07-30 12:13:40', 'co2': 406, 'temperature': 20, 'humidity': 61}
2025-07-30 12:13:40,911 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848820, 'datetime': '2025-07-30 12:13:40', 'co2': 436, 'temperature': 22, 'humidity': 75}
2025-07-30 12:13:40,912 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:40,913 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848820, 'datetime': '2025-07-30 12:13:40', 'co2': 553, 'temperature': 31, 'humidity': 77}
2025-07-30 12:13:40,914 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:40,915 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:40,917 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848820, 'datetime': '2025-07-30 12:13:40', 'co2': 546, 'temperature': 24, 'humidity': 77}
2025-07-30 12:13:40,918 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848820, 'datetime': '2025-07-30 12:13:40', 'co2': 421, 'temperature': 25, 'humidity': 51}
2025-07-30 12:13:40,920 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:40,920 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:51,910 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848831, 'datetime': '2025-07-30 12:13:51', 'co2': 587, 'temperature': 29, 'humidity': 55}
2025-07-30 12:13:51,911 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848831, 'datetime': '2025-07-30 12:13:51', 'co2': 445, 'temperature': 27, 'humidity': 61}
2025-07-30 12:13:51,912 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848831, 'datetime': '2025-07-30 12:13:51', 'co2': 512, 'temperature': 32, 'humidity': 67}
2025-07-30 12:13:51,913 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753848831, 缓存大小: 2/1000
2025-07-30 12:13:51,914 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848831, 'datetime': '2025-07-30 12:13:51', 'co2': 540, 'temperature': 23, 'humidity': 76}
2025-07-30 12:13:51,916 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848831, 'datetime': '2025-07-30 12:13:51', 'co2': 570, 'temperature': 23, 'humidity': 51}
2025-07-30 12:13:51,917 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:51,917 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:51,917 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:51,917 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:51,918 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848831, 'datetime': '2025-07-30 12:13:51', 'co2': 560, 'temperature': 20, 'humidity': 78}
2025-07-30 12:13:51,919 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:51,922 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848831, 'datetime': '2025-07-30 12:13:51', 'co2': 514, 'temperature': 31, 'humidity': 56}
2025-07-30 12:13:51,923 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848831, 'datetime': '2025-07-30 12:13:51', 'co2': 513, 'temperature': 30, 'humidity': 51}
2025-07-30 12:13:51,925 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848831, 'datetime': '2025-07-30 12:13:51', 'co2': 531, 'temperature': 23, 'humidity': 66}
2025-07-30 12:13:51,927 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848831, 'datetime': '2025-07-30 12:13:51', 'co2': 442, 'temperature': 28, 'humidity': 79}
2025-07-30 12:13:51,929 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:51,933 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:51,933 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:51,934 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:51,935 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:14:04,103 - sensor_collector - INFO - --- 开始传感器数据采集周期 ---
2025-07-30 12:14:04,103 - sensor_collector - INFO - 开始采集传感器数据: 2025-07-30 12:14:04
2025-07-30 12:14:04,103 - sensor_collector - INFO - 开始采集温湿度CO2数据...
2025-07-30 12:14:04,104 - sensor_collector - INFO - 温湿度CO2数据采集成功
2025-07-30 12:14:04,104 - sensor_collector - INFO - 开始采集GPS数据...
2025-07-30 12:14:04,104 - sensor_collector - INFO - GPS数据采集成功
2025-07-30 12:14:04,104 - sensor_collector - INFO - 开始采集风速数据...
2025-07-30 12:14:04,104 - sensor_collector - INFO - 风速数据采集成功
2025-07-30 12:14:04,104 - sensor_collector - INFO - 成功采集到 6 项传感器数据
2025-07-30 12:14:04,105 - sensor_collector - INFO - 完成数据质量验证
2025-07-30 12:14:04,105 - sensor_collector - ERROR - 保存传感器数据失败: '<' not supported between instances of 'dict' and 'int'
Traceback (most recent call last):
  File "/home/<USER>/main_test/sensors/sensor_collector.py", line 976, in save_sensor_data
    data_valid, data_modified = self.verify_data_quality(data, is_api_data)
  File "/home/<USER>/main_test/sensors/sensor_collector.py", line 901, in verify_data_quality
    if temp < temp_min or temp > temp_max:
TypeError: '<' not supported between instances of 'dict' and 'int'
2025-07-30 12:14:34,102 - sensor_collector - INFO - 已将 2 条传感器数据提交到异步写入队列
2025-07-30 12:14:36,558 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848876, 'datetime': '2025-07-30 12:14:36', 'co2': 619, 'temperature': 22, 'humidity': 78}
2025-07-30 12:14:36,559 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753848876, 缓存大小: 1/1000
2025-07-30 12:14:36,559 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:14:37,079 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848877, 'datetime': '2025-07-30 12:14:37', 'co2': 521, 'temperature': 36, 'humidity': 43}
2025-07-30 12:14:37,079 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753848877, 缓存大小: 2/1000
2025-07-30 12:14:37,080 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:14:37,598 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848877, 'datetime': '2025-07-30 12:14:37', 'co2': 638, 'temperature': 28, 'humidity': 71}
2025-07-30 12:14:37,598 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:14:38,116 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848878, 'datetime': '2025-07-30 12:14:38', 'co2': 527, 'temperature': 35, 'humidity': 51}
2025-07-30 12:14:38,116 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753848878, 缓存大小: 3/1000
2025-07-30 12:14:38,116 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:14:38,634 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848878, 'datetime': '2025-07-30 12:14:38', 'co2': 464, 'temperature': 34, 'humidity': 69}
2025-07-30 12:14:38,635 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:14:39,153 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848879, 'datetime': '2025-07-30 12:14:39', 'co2': 638, 'temperature': 37, 'humidity': 65}
2025-07-30 12:14:39,153 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753848879, 缓存大小: 4/1000
2025-07-30 12:14:39,153 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:14:39,672 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848879, 'datetime': '2025-07-30 12:14:39', 'co2': 475, 'temperature': 31, 'humidity': 72}
2025-07-30 12:14:39,672 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:14:40,190 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848880, 'datetime': '2025-07-30 12:14:40', 'co2': 649, 'temperature': 39, 'humidity': 56}
2025-07-30 12:14:40,190 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753848880, 缓存大小: 5/1000
2025-07-30 12:14:40,190 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:14:40,708 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848880, 'datetime': '2025-07-30 12:14:40', 'co2': 582, 'temperature': 31, 'humidity': 74}
2025-07-30 12:14:40,709 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:14:41,227 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848881, 'datetime': '2025-07-30 12:14:41', 'co2': 403, 'temperature': 24, 'humidity': 40}
2025-07-30 12:14:41,228 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753848881, 缓存大小: 6/1000
2025-07-30 12:14:41,228 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:14:41,746 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848881, 'datetime': '2025-07-30 12:14:41', 'co2': 589, 'temperature': 34, 'humidity': 47}
2025-07-30 12:14:41,747 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:14:42,265 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848882, 'datetime': '2025-07-30 12:14:42', 'co2': 683, 'temperature': 29, 'humidity': 49}
2025-07-30 12:14:42,266 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753848882, 缓存大小: 7/1000
2025-07-30 12:14:42,266 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:14:42,784 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848882, 'datetime': '2025-07-30 12:14:42', 'co2': 482, 'temperature': 24, 'humidity': 40}
2025-07-30 12:14:42,786 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:14:43,307 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848883, 'datetime': '2025-07-30 12:14:43', 'co2': 658, 'temperature': 34, 'humidity': 65}
2025-07-30 12:14:43,308 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753848883, 缓存大小: 8/1000
2025-07-30 12:14:43,308 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:14:43,826 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848883, 'datetime': '2025-07-30 12:14:43', 'co2': 544, 'temperature': 31, 'humidity': 70}
2025-07-30 12:14:43,827 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:15:04,167 - sensor_collector - INFO - --- 开始传感器数据采集周期 ---
2025-07-30 12:15:04,167 - sensor_collector - INFO - 开始采集传感器数据: 2025-07-30 12:15:04
2025-07-30 12:15:04,168 - sensor_collector - INFO - 开始采集温湿度CO2数据...
2025-07-30 12:15:04,168 - sensor_collector - INFO - 温湿度CO2数据采集成功
2025-07-30 12:15:04,168 - sensor_collector - INFO - 开始采集GPS数据...
2025-07-30 12:15:04,168 - sensor_collector - INFO - GPS数据采集成功
2025-07-30 12:15:04,168 - sensor_collector - INFO - 开始采集风速数据...
2025-07-30 12:15:04,168 - sensor_collector - INFO - 风速数据采集成功
2025-07-30 12:15:04,168 - sensor_collector - INFO - 成功采集到 6 项传感器数据
2025-07-30 12:15:04,169 - sensor_collector - INFO - 完成数据质量验证
2025-07-30 12:15:04,169 - sensor_collector - ERROR - 保存传感器数据失败: '<' not supported between instances of 'dict' and 'int'
Traceback (most recent call last):
  File "/home/<USER>/main_test/sensors/sensor_collector.py", line 976, in save_sensor_data
    data_valid, data_modified = self.verify_data_quality(data, is_api_data)
  File "/home/<USER>/main_test/sensors/sensor_collector.py", line 901, in verify_data_quality
    if temp < temp_min or temp > temp_max:
TypeError: '<' not supported between instances of 'dict' and 'int'
2025-07-30 12:15:34,155 - sensor_collector - INFO - 已将 8 条传感器数据提交到异步写入队列
2025-07-30 12:15:44,397 - sensor_collector - INFO - 
正在停止传感器数据采集线程...
2025-07-30 12:15:44,397 - sensor_collector - INFO - 已取消传感器数据采集任务
2025-07-30 12:15:44,397 - sensor_collector - INFO - 已取消传感器缓存刷新定期任务
2025-07-30 12:15:44,397 - sensor_collector - INFO - 执行停止前的缓存刷新...
2025-07-30 12:15:44,397 - sensor_collector - INFO - 执行停止前的数据强制备份...
2025-07-30 12:15:44,400 - sensor_collector - INFO - 已创建数据备份: /home/<USER>/main_test/test_data/sensors/current/sensor_data.json.20250730121544.bak
2025-07-30 12:15:45,194 - sensor_collector - INFO - 🧪 模拟硬件控制器已停止
2025-07-30 12:15:45,194 - sensor_collector - INFO - 传感器数据采集线程已停止
2025-07-30 12:58:49,858 - sensor_collector - INFO - 初始化传感器收集器...
2025-07-30 12:58:49,858 - sensor_collector - INFO - 🧪 传感器收集器运行在测试模式
2025-07-30 12:58:49,858 - sensor_collector - INFO - 🧪 启用模拟硬件组件
2025-07-30 12:58:49,858 - sensor_collector - INFO - 串口配置: 端口=/dev/mock_ttyTHS1, 波特率=4800
2025-07-30 12:58:49,859 - sensor_collector - INFO - 🧪 测试模式数据目录: /home/<USER>/main_test/test_data/sensors
2025-07-30 12:58:49,859 - sensor_collector - INFO - 当前运行目录: /home/<USER>/main_test
2025-07-30 12:58:49,859 - sensor_collector - INFO - 项目根目录: /home/<USER>/main_test
2025-07-30 12:58:49,859 - sensor_collector - INFO - 数据目录: /home/<USER>/main_test/test_data/sensors
2025-07-30 12:58:49,859 - sensor_collector - INFO - 数据目录写入权限测试成功
2025-07-30 12:58:49,864 - sensor_collector - INFO - 🧪 模拟硬件控制器初始化成功
2025-07-30 12:58:49,864 - sensor_collector - INFO - 🧪 测试模式：跳过真实串口初始化
2025-07-30 12:58:49,868 - sensor_collector - INFO - 已注册内存监控回调
2025-07-30 12:58:49,886 - sensor_collector - INFO - 异步文件I/O初始化完成
2025-07-30 12:58:49,898 - sensor_collector - INFO - 线程管理器初始化完成
2025-07-30 12:58:49,909 - sensor_collector - INFO - 线程管理器初始化完成
2025-07-30 12:58:49,910 - sensor_collector - INFO - [采集线程] 开始传感器数据采集循环(采集间隔:60秒), 线程ID=281473659367712
2025-07-30 12:58:49,910 - sensor_collector - INFO - 传感器数据采集任务已提交到线程管理器
2025-07-30 12:58:49,910 - sensor_collector - INFO - --- 开始传感器数据采集周期 ---
2025-07-30 12:58:49,911 - sensor_collector - INFO - 开始采集传感器数据: 2025-07-30 12:58:49
2025-07-30 12:58:49,911 - sensor_collector - INFO - 开始采集温湿度CO2数据...
2025-07-30 12:58:49,911 - sensor_collector - INFO - cth传感器状态变化: 断开连接 → 正常
2025-07-30 12:58:49,912 - sensor_collector - INFO - 温湿度CO2数据采集成功
2025-07-30 12:58:49,912 - sensor_collector - INFO - 开始采集GPS数据...
2025-07-30 12:58:49,912 - sensor_collector - INFO - gps传感器状态变化: 断开连接 → 正常
2025-07-30 12:58:49,913 - sensor_collector - INFO - GPS数据采集成功
2025-07-30 12:58:49,913 - sensor_collector - INFO - 开始采集风速数据...
2025-07-30 12:58:49,913 - sensor_collector - INFO - ws传感器状态变化: 断开连接 → 正常
2025-07-30 12:58:49,913 - sensor_collector - INFO - 风速数据采集成功
2025-07-30 12:58:49,914 - sensor_collector - INFO - 成功采集到 6 项传感器数据
2025-07-30 12:58:49,914 - sensor_collector - INFO - 完成数据质量验证
2025-07-30 12:58:49,914 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851529, 缓存大小: 1/1000
2025-07-30 12:59:19,935 - sensor_collector - INFO - 已将 1 条传感器数据提交到异步写入队列
2025-07-30 12:59:49,975 - sensor_collector - INFO - --- 开始传感器数据采集周期 ---
2025-07-30 12:59:49,975 - sensor_collector - INFO - 开始采集传感器数据: 2025-07-30 12:59:49
2025-07-30 12:59:49,976 - sensor_collector - INFO - 开始采集温湿度CO2数据...
2025-07-30 12:59:49,976 - sensor_collector - INFO - 温湿度CO2数据采集成功
2025-07-30 12:59:49,976 - sensor_collector - INFO - 开始采集GPS数据...
2025-07-30 12:59:49,976 - sensor_collector - INFO - GPS数据采集成功
2025-07-30 12:59:49,976 - sensor_collector - INFO - 开始采集风速数据...
2025-07-30 12:59:49,977 - sensor_collector - INFO - 风速数据采集成功
2025-07-30 12:59:49,977 - sensor_collector - INFO - 成功采集到 6 项传感器数据
2025-07-30 12:59:49,977 - sensor_collector - INFO - 完成数据质量验证
2025-07-30 12:59:49,977 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851589, 缓存大小: 1/1000
2025-07-30 13:00:19,970 - sensor_collector - INFO - 已将 1 条传感器数据提交到异步写入队列
2025-07-30 13:00:50,038 - sensor_collector - INFO - --- 开始传感器数据采集周期 ---
2025-07-30 13:00:50,038 - sensor_collector - INFO - 开始采集传感器数据: 2025-07-30 13:00:50
2025-07-30 13:00:50,038 - sensor_collector - INFO - 开始采集温湿度CO2数据...
2025-07-30 13:00:50,039 - sensor_collector - INFO - 温湿度CO2数据采集成功
2025-07-30 13:00:50,039 - sensor_collector - INFO - 开始采集GPS数据...
2025-07-30 13:00:50,039 - sensor_collector - INFO - GPS数据采集成功
2025-07-30 13:00:50,039 - sensor_collector - INFO - 开始采集风速数据...
2025-07-30 13:00:50,039 - sensor_collector - INFO - 风速数据采集成功
2025-07-30 13:00:50,039 - sensor_collector - INFO - 成功采集到 6 项传感器数据
2025-07-30 13:00:50,040 - sensor_collector - INFO - 完成数据质量验证
2025-07-30 13:00:50,040 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851650, 缓存大小: 1/1000
2025-07-30 13:00:53,371 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851653, 'datetime': '2025-07-30 13:00:53', 'temperature': 25.5, 'humidity': 60.2, 'co2': 450}
2025-07-30 13:00:53,372 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851653, 缓存大小: 2/1000
2025-07-30 13:00:53,372 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:01:20,026 - sensor_collector - INFO - 已将 2 条传感器数据提交到异步写入队列
2025-07-30 13:01:50,098 - sensor_collector - INFO - --- 开始传感器数据采集周期 ---
2025-07-30 13:01:50,098 - sensor_collector - INFO - 开始采集传感器数据: 2025-07-30 13:01:50
2025-07-30 13:01:50,099 - sensor_collector - INFO - 开始采集温湿度CO2数据...
2025-07-30 13:01:50,099 - sensor_collector - INFO - 温湿度CO2数据采集成功
2025-07-30 13:01:50,099 - sensor_collector - INFO - 开始采集GPS数据...
2025-07-30 13:01:50,099 - sensor_collector - INFO - GPS数据采集成功
2025-07-30 13:01:50,099 - sensor_collector - INFO - 开始采集风速数据...
2025-07-30 13:01:50,099 - sensor_collector - INFO - 风速数据采集成功
2025-07-30 13:01:50,100 - sensor_collector - INFO - 成功采集到 6 项传感器数据
2025-07-30 13:01:50,100 - sensor_collector - INFO - 完成数据质量验证
2025-07-30 13:01:50,100 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851710, 缓存大小: 1/1000
2025-07-30 13:02:20,082 - sensor_collector - INFO - 已将 1 条传感器数据提交到异步写入队列
2025-07-30 13:02:50,160 - sensor_collector - INFO - --- 开始传感器数据采集周期 ---
2025-07-30 13:02:50,160 - sensor_collector - INFO - 开始采集传感器数据: 2025-07-30 13:02:50
2025-07-30 13:02:50,161 - sensor_collector - INFO - 开始采集温湿度CO2数据...
2025-07-30 13:02:50,161 - sensor_collector - INFO - 温湿度CO2数据采集成功
2025-07-30 13:02:50,161 - sensor_collector - INFO - 开始采集GPS数据...
2025-07-30 13:02:50,161 - sensor_collector - INFO - GPS数据采集成功
2025-07-30 13:02:50,161 - sensor_collector - INFO - 开始采集风速数据...
2025-07-30 13:02:50,162 - sensor_collector - INFO - 风速数据采集成功
2025-07-30 13:02:50,162 - sensor_collector - INFO - 成功采集到 6 项传感器数据
2025-07-30 13:02:50,162 - sensor_collector - INFO - 完成数据质量验证
2025-07-30 13:02:50,162 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851770, 缓存大小: 1/1000
2025-07-30 13:03:20,138 - sensor_collector - INFO - 已将 1 条传感器数据提交到异步写入队列
2025-07-30 13:03:35,740 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851815, 'datetime': '2025-07-30 13:03:35', 'temperature': 25.5, 'humidity': 60.2, 'co2': 450}
2025-07-30 13:03:35,741 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851815, 缓存大小: 1/1000
2025-07-30 13:03:35,741 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:03:35,759 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851815, 'datetime': '2025-07-30 13:03:35', 'temperature': {'value': 22, 'anomaly': 'sudden_change'}, 'humidity': 65, 'co2': 500}
2025-07-30 13:03:35,760 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:03:44,119 - sensor_collector - INFO - 接收到测试数据注入: {'temperature': 23.1, 'humidity': 58.3, 'co2': 420, 'timestamp': 1753851824, 'datetime': '2025-07-30 13:03:44'}
2025-07-30 13:03:44,120 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851824, 缓存大小: 2/1000
2025-07-30 13:03:44,120 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:03:44,120 - sensor_collector - INFO - 接收到测试数据注入: {'temperature': {'value': 45, 'anomaly': 'extreme'}, 'humidity': 70, 'co2': 600, 'timestamp': 1753851825, 'datetime': '2025-07-30 13:03:44'}
2025-07-30 13:03:44,120 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851825, 缓存大小: 3/1000
2025-07-30 13:03:44,121 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:03:44,121 - sensor_collector - INFO - 接收到测试数据注入: {'temperature': 24.5, 'humidity': {'value': 95, 'anomaly': 'sudden_change'}, 'co2': 480, 'timestamp': 1753851826, 'datetime': '2025-07-30 13:03:44'}
2025-07-30 13:03:44,121 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851826, 缓存大小: 4/1000
2025-07-30 13:03:44,121 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:03:44,121 - sensor_collector - INFO - 接收到测试数据注入: {'temperature': 22.8, 'humidity': 62.1, 'co2': {'value': 2000, 'anomaly': 'extreme'}, 'timestamp': 1753851827, 'datetime': '2025-07-30 13:03:44'}
2025-07-30 13:03:44,121 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851827, 缓存大小: 5/1000
2025-07-30 13:03:44,121 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:03:44,121 - sensor_collector - INFO - 接收到测试数据注入: {'temperature': 25.2, 'humidity': 59.8, 'co2': 440, 'timestamp': 1753851828, 'datetime': '2025-07-30 13:03:44'}
2025-07-30 13:03:44,121 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851828, 缓存大小: 6/1000
2025-07-30 13:03:44,122 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:03:50,223 - sensor_collector - INFO - --- 开始传感器数据采集周期 ---
2025-07-30 13:03:50,224 - sensor_collector - INFO - 开始采集传感器数据: 2025-07-30 13:03:50
2025-07-30 13:03:50,224 - sensor_collector - INFO - 开始采集温湿度CO2数据...
2025-07-30 13:03:50,224 - sensor_collector - INFO - 温湿度CO2数据采集成功
2025-07-30 13:03:50,224 - sensor_collector - INFO - 开始采集GPS数据...
2025-07-30 13:03:50,225 - sensor_collector - INFO - GPS数据采集成功
2025-07-30 13:03:50,225 - sensor_collector - INFO - 开始采集风速数据...
2025-07-30 13:03:50,225 - sensor_collector - INFO - 风速数据采集成功
2025-07-30 13:03:50,225 - sensor_collector - INFO - 成功采集到 6 项传感器数据
2025-07-30 13:03:50,225 - sensor_collector - INFO - 完成数据质量验证
2025-07-30 13:03:50,226 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851830, 缓存大小: 7/1000
2025-07-30 13:04:02,222 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851842, 'datetime': '2025-07-30 13:04:02', 'temperature': 25, 'humidity': 66, 'co2': 660}
2025-07-30 13:04:02,222 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851842, 缓存大小: 8/1000
2025-07-30 13:04:02,222 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:04:04,243 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851844, 'datetime': '2025-07-30 13:04:04', 'temperature': 29, 'humidity': 78, 'co2': 545}
2025-07-30 13:04:04,243 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851844, 缓存大小: 9/1000
2025-07-30 13:04:04,243 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:04:06,264 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851846, 'datetime': '2025-07-30 13:04:06', 'temperature': 28, 'humidity': 69, 'co2': 970}
2025-07-30 13:04:06,264 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851846, 缓存大小: 10/1000
2025-07-30 13:04:06,264 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:04:08,286 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851848, 'datetime': '2025-07-30 13:04:08', 'temperature': 33, 'humidity': 56, 'co2': 779}
2025-07-30 13:04:08,286 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851848, 缓存大小: 11/1000
2025-07-30 13:04:08,286 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:04:10,308 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851850, 'datetime': '2025-07-30 13:04:10', 'temperature': 20, 'humidity': 41, 'co2': 1001}
2025-07-30 13:04:10,308 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851850, 缓存大小: 12/1000
2025-07-30 13:04:10,308 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:04:12,329 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851852, 'datetime': '2025-07-30 13:04:12', 'temperature': 21, 'humidity': 46, 'co2': 377}
2025-07-30 13:04:12,329 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851852, 缓存大小: 13/1000
2025-07-30 13:04:12,329 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:04:14,349 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851854, 'datetime': '2025-07-30 13:04:14', 'temperature': 21, 'humidity': 72, 'co2': 495}
2025-07-30 13:04:14,350 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851854, 缓存大小: 14/1000
2025-07-30 13:04:14,350 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:04:16,371 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851856, 'datetime': '2025-07-30 13:04:16', 'temperature': 24, 'humidity': 75, 'co2': 349}
2025-07-30 13:04:16,372 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851856, 缓存大小: 15/1000
2025-07-30 13:04:16,372 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:04:18,394 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851858, 'datetime': '2025-07-30 13:04:18', 'temperature': 20, 'humidity': 58, 'co2': 300}
2025-07-30 13:04:18,394 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851858, 缓存大小: 16/1000
2025-07-30 13:04:18,394 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:04:20,194 - sensor_collector - INFO - 已将 16 条传感器数据提交到异步写入队列
2025-07-30 13:04:20,416 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851860, 'datetime': '2025-07-30 13:04:20', 'temperature': 22, 'humidity': 52, 'co2': 440}
2025-07-30 13:04:20,416 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851860, 缓存大小: 1/1000
2025-07-30 13:04:20,416 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:04:22,438 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851862, 'datetime': '2025-07-30 13:04:22', 'temperature': 21, 'humidity': 71, 'co2': 1170}
2025-07-30 13:04:22,439 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851862, 缓存大小: 2/1000
2025-07-30 13:04:22,439 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:04:24,460 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851864, 'datetime': '2025-07-30 13:04:24', 'temperature': 26, 'humidity': 73, 'co2': 964}
2025-07-30 13:04:24,460 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851864, 缓存大小: 3/1000
2025-07-30 13:04:24,460 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:04:26,481 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851866, 'datetime': '2025-07-30 13:04:26', 'temperature': 23, 'humidity': 45, 'co2': 905}
2025-07-30 13:04:26,481 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851866, 缓存大小: 4/1000
2025-07-30 13:04:26,481 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:04:27,255 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851867, 'datetime': '2025-07-30 13:04:27', 'temperature': 32, 'humidity': 71, 'co2': 693}
2025-07-30 13:04:27,255 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851867, 缓存大小: 5/1000
2025-07-30 13:04:27,256 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:04:28,502 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851868, 'datetime': '2025-07-30 13:04:28', 'temperature': 28, 'humidity': 76, 'co2': 1097}
2025-07-30 13:04:28,502 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851868, 缓存大小: 6/1000
2025-07-30 13:04:28,503 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:04:29,276 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851869, 'datetime': '2025-07-30 13:04:29', 'temperature': 33, 'humidity': 77, 'co2': 309}
2025-07-30 13:04:29,277 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851869, 缓存大小: 7/1000
2025-07-30 13:04:29,277 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:04:30,526 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851870, 'datetime': '2025-07-30 13:04:30', 'temperature': 20, 'humidity': 76, 'co2': 1175}
2025-07-30 13:04:30,526 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851870, 缓存大小: 8/1000
2025-07-30 13:04:30,526 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:04:31,297 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851871, 'datetime': '2025-07-30 13:04:31', 'temperature': 25, 'humidity': 77, 'co2': 491}
2025-07-30 13:04:31,297 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851871, 缓存大小: 9/1000
2025-07-30 13:04:31,297 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:04:32,546 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851872, 'datetime': '2025-07-30 13:04:32', 'temperature': 28, 'humidity': 63, 'co2': 631}
2025-07-30 13:04:32,547 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851872, 缓存大小: 10/1000
2025-07-30 13:04:32,547 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:04:33,319 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851873, 'datetime': '2025-07-30 13:04:33', 'temperature': 33, 'humidity': 64, 'co2': 893}
2025-07-30 13:04:33,319 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851873, 缓存大小: 11/1000
2025-07-30 13:04:33,320 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:04:34,568 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851874, 'datetime': '2025-07-30 13:04:34', 'temperature': 29, 'humidity': 66, 'co2': 871}
2025-07-30 13:04:34,568 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851874, 缓存大小: 12/1000
2025-07-30 13:04:34,568 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:04:35,342 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851875, 'datetime': '2025-07-30 13:04:35', 'temperature': 20, 'humidity': 70, 'co2': 989}
2025-07-30 13:04:35,342 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851875, 缓存大小: 13/1000
2025-07-30 13:04:35,342 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:04:36,591 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851876, 'datetime': '2025-07-30 13:04:36', 'temperature': 30, 'humidity': 49, 'co2': 643}
2025-07-30 13:04:36,591 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851876, 缓存大小: 14/1000
2025-07-30 13:04:36,592 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:04:37,363 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851877, 'datetime': '2025-07-30 13:04:37', 'temperature': 25, 'humidity': 49, 'co2': 882}
2025-07-30 13:04:37,363 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851877, 缓存大小: 15/1000
2025-07-30 13:04:37,364 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:04:38,596 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851878, 'datetime': '2025-07-30 13:04:38', 'temperature': 30, 'humidity': 66, 'co2': 1139}
2025-07-30 13:04:38,597 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851878, 缓存大小: 16/1000
2025-07-30 13:04:38,597 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:04:38,613 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851878, 'datetime': '2025-07-30 13:04:38', 'temperature': 33, 'humidity': 56, 'co2': 807}
2025-07-30 13:04:38,614 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:04:39,385 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851879, 'datetime': '2025-07-30 13:04:39', 'temperature': 32, 'humidity': 71, 'co2': 913}
2025-07-30 13:04:39,385 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851879, 缓存大小: 17/1000
2025-07-30 13:04:39,385 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:04:40,617 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851880, 'datetime': '2025-07-30 13:04:40', 'temperature': 28, 'humidity': 53, 'co2': 898}
2025-07-30 13:04:40,617 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851880, 缓存大小: 18/1000
2025-07-30 13:04:40,617 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:04:40,634 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851880, 'datetime': '2025-07-30 13:04:40', 'temperature': 26, 'humidity': 50, 'co2': 531}
2025-07-30 13:04:40,635 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:04:41,407 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851881, 'datetime': '2025-07-30 13:04:41', 'temperature': 25, 'humidity': 75, 'co2': 857}
2025-07-30 13:04:41,407 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851881, 缓存大小: 19/1000
2025-07-30 13:04:41,407 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:04:42,637 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851882, 'datetime': '2025-07-30 13:04:42', 'temperature': 23, 'humidity': 70, 'co2': 1107}
2025-07-30 13:04:42,637 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851882, 缓存大小: 20/1000
2025-07-30 13:04:42,638 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:04:42,655 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851882, 'datetime': '2025-07-30 13:04:42', 'temperature': 22, 'humidity': 76, 'co2': 1143}
2025-07-30 13:04:42,655 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:04:43,428 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851883, 'datetime': '2025-07-30 13:04:43', 'temperature': 34, 'humidity': 49, 'co2': 1004}
2025-07-30 13:04:43,428 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851883, 缓存大小: 21/1000
2025-07-30 13:04:43,428 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:04:44,659 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851884, 'datetime': '2025-07-30 13:04:44', 'temperature': 23, 'humidity': 71, 'co2': 721}
2025-07-30 13:04:44,659 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851884, 缓存大小: 22/1000
2025-07-30 13:04:44,660 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:04:44,677 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851884, 'datetime': '2025-07-30 13:04:44', 'temperature': 23, 'humidity': 54, 'co2': 1177}
2025-07-30 13:04:44,677 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:04:45,448 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851885, 'datetime': '2025-07-30 13:04:45', 'temperature': 24, 'humidity': 43, 'co2': 594}
2025-07-30 13:04:45,448 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851885, 缓存大小: 23/1000
2025-07-30 13:04:45,448 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:04:46,680 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851886, 'datetime': '2025-07-30 13:04:46', 'temperature': 30, 'humidity': 46, 'co2': 406}
2025-07-30 13:04:46,680 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851886, 缓存大小: 24/1000
2025-07-30 13:04:46,680 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:04:46,698 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851886, 'datetime': '2025-07-30 13:04:46', 'temperature': 20, 'humidity': 43, 'co2': 576}
2025-07-30 13:04:46,699 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:04:47,469 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851887, 'datetime': '2025-07-30 13:04:47', 'temperature': 22, 'humidity': 77, 'co2': 500}
2025-07-30 13:04:47,469 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851887, 缓存大小: 25/1000
2025-07-30 13:04:47,470 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:04:48,701 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851888, 'datetime': '2025-07-30 13:04:48', 'temperature': 28, 'humidity': 44, 'co2': 718}
2025-07-30 13:04:48,702 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851888, 缓存大小: 26/1000
2025-07-30 13:04:48,702 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:04:48,719 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851888, 'datetime': '2025-07-30 13:04:48', 'temperature': 30, 'humidity': 47, 'co2': 366}
2025-07-30 13:04:48,720 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:04:49,491 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851889, 'datetime': '2025-07-30 13:04:49', 'temperature': 27, 'humidity': 45, 'co2': 444}
2025-07-30 13:04:49,491 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851889, 缓存大小: 27/1000
2025-07-30 13:04:49,491 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:04:50,286 - sensor_collector - INFO - --- 开始传感器数据采集周期 ---
2025-07-30 13:04:50,286 - sensor_collector - INFO - 开始采集传感器数据: 2025-07-30 13:04:50
2025-07-30 13:04:50,287 - sensor_collector - INFO - 开始采集温湿度CO2数据...
2025-07-30 13:04:50,287 - sensor_collector - INFO - 温湿度CO2数据采集成功
2025-07-30 13:04:50,287 - sensor_collector - INFO - 开始采集GPS数据...
2025-07-30 13:04:50,287 - sensor_collector - INFO - GPS数据采集成功
2025-07-30 13:04:50,288 - sensor_collector - INFO - 开始采集风速数据...
2025-07-30 13:04:50,288 - sensor_collector - INFO - 风速数据采集成功
2025-07-30 13:04:50,288 - sensor_collector - INFO - 成功采集到 6 项传感器数据
2025-07-30 13:04:50,289 - sensor_collector - INFO - 完成数据质量验证
2025-07-30 13:04:50,289 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851890, 缓存大小: 28/1000
2025-07-30 13:04:50,722 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851890, 'datetime': '2025-07-30 13:04:50', 'temperature': 29, 'humidity': 56, 'co2': 1181}
2025-07-30 13:04:50,722 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:04:50,739 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851890, 'datetime': '2025-07-30 13:04:50', 'temperature': 26, 'humidity': 40, 'co2': 368}
2025-07-30 13:04:50,740 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:04:51,513 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851891, 'datetime': '2025-07-30 13:04:51', 'temperature': 20, 'humidity': 51, 'co2': 632}
2025-07-30 13:04:51,513 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851891, 缓存大小: 29/1000
2025-07-30 13:04:51,513 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:04:52,742 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851892, 'datetime': '2025-07-30 13:04:52', 'temperature': 20, 'humidity': 76, 'co2': 635}
2025-07-30 13:04:52,742 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851892, 缓存大小: 30/1000
2025-07-30 13:04:52,742 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:04:52,760 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851892, 'datetime': '2025-07-30 13:04:52', 'temperature': 24, 'humidity': 61, 'co2': 921}
2025-07-30 13:04:52,760 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:04:53,534 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851893, 'datetime': '2025-07-30 13:04:53', 'temperature': 30, 'humidity': 76, 'co2': 976}
2025-07-30 13:04:53,535 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851893, 缓存大小: 31/1000
2025-07-30 13:04:53,535 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:04:54,763 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851894, 'datetime': '2025-07-30 13:04:54', 'temperature': 27, 'humidity': 76, 'co2': 925}
2025-07-30 13:04:54,763 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851894, 缓存大小: 32/1000
2025-07-30 13:04:54,763 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:04:54,780 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851894, 'datetime': '2025-07-30 13:04:54', 'temperature': 25, 'humidity': 48, 'co2': 817}
2025-07-30 13:04:54,781 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:04:55,556 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851895, 'datetime': '2025-07-30 13:04:55', 'temperature': 22, 'humidity': 55, 'co2': 509}
2025-07-30 13:04:55,557 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851895, 缓存大小: 33/1000
2025-07-30 13:04:55,557 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:04:56,785 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851896, 'datetime': '2025-07-30 13:04:56', 'temperature': 21, 'humidity': 61, 'co2': 502}
2025-07-30 13:04:56,785 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851896, 缓存大小: 34/1000
2025-07-30 13:04:56,786 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:04:56,801 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851896, 'datetime': '2025-07-30 13:04:56', 'temperature': 23, 'humidity': 46, 'co2': 896}
2025-07-30 13:04:56,802 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:04:57,579 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851897, 'datetime': '2025-07-30 13:04:57', 'temperature': 33, 'humidity': 57, 'co2': 935}
2025-07-30 13:04:57,580 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851897, 缓存大小: 35/1000
2025-07-30 13:04:57,580 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:04:58,806 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851898, 'datetime': '2025-07-30 13:04:58', 'temperature': 23, 'humidity': 41, 'co2': 995}
2025-07-30 13:04:58,807 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851898, 缓存大小: 36/1000
2025-07-30 13:04:58,809 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:04:58,825 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851898, 'datetime': '2025-07-30 13:04:58', 'temperature': 34, 'humidity': 42, 'co2': 1038}
2025-07-30 13:04:58,826 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:04:59,603 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851899, 'datetime': '2025-07-30 13:04:59', 'temperature': 31, 'humidity': 63, 'co2': 678}
2025-07-30 13:04:59,603 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851899, 缓存大小: 37/1000
2025-07-30 13:04:59,604 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:05:00,830 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851900, 'datetime': '2025-07-30 13:05:00', 'temperature': 25, 'humidity': 45, 'co2': 1148}
2025-07-30 13:05:00,831 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851900, 缓存大小: 38/1000
2025-07-30 13:05:00,831 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:05:00,846 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851900, 'datetime': '2025-07-30 13:05:00', 'temperature': 30, 'humidity': 62, 'co2': 889}
2025-07-30 13:05:00,846 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:05:01,625 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851901, 'datetime': '2025-07-30 13:05:01', 'temperature': 32, 'humidity': 60, 'co2': 654}
2025-07-30 13:05:01,625 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851901, 缓存大小: 39/1000
2025-07-30 13:05:01,625 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:05:02,851 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851902, 'datetime': '2025-07-30 13:05:02', 'temperature': 27, 'humidity': 42, 'co2': 697}
2025-07-30 13:05:02,852 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851902, 缓存大小: 40/1000
2025-07-30 13:05:02,852 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:05:03,645 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851903, 'datetime': '2025-07-30 13:05:03', 'temperature': 31, 'humidity': 48, 'co2': 760}
2025-07-30 13:05:03,646 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851903, 缓存大小: 41/1000
2025-07-30 13:05:03,646 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:05:04,872 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851904, 'datetime': '2025-07-30 13:05:04', 'temperature': 25, 'humidity': 56, 'co2': 922}
2025-07-30 13:05:04,872 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851904, 缓存大小: 42/1000
2025-07-30 13:05:04,872 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:05:05,666 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851905, 'datetime': '2025-07-30 13:05:05', 'temperature': 33, 'humidity': 43, 'co2': 1055}
2025-07-30 13:05:05,666 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851905, 缓存大小: 43/1000
2025-07-30 13:05:05,667 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:05:06,894 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851906, 'datetime': '2025-07-30 13:05:06', 'temperature': 30, 'humidity': 67, 'co2': 579}
2025-07-30 13:05:06,895 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851906, 缓存大小: 44/1000
2025-07-30 13:05:06,895 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:05:07,687 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851907, 'datetime': '2025-07-30 13:05:07', 'temperature': 26, 'humidity': 69, 'co2': 725}
2025-07-30 13:05:07,688 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851907, 缓存大小: 45/1000
2025-07-30 13:05:07,688 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:05:08,916 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851908, 'datetime': '2025-07-30 13:05:08', 'temperature': 31, 'humidity': 72, 'co2': 1086}
2025-07-30 13:05:08,916 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851908, 缓存大小: 46/1000
2025-07-30 13:05:08,917 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:05:09,709 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851909, 'datetime': '2025-07-30 13:05:09', 'temperature': 30, 'humidity': 78, 'co2': 878}
2025-07-30 13:05:09,709 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851909, 缓存大小: 47/1000
2025-07-30 13:05:09,710 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:05:10,938 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851910, 'datetime': '2025-07-30 13:05:10', 'temperature': 34, 'humidity': 68, 'co2': 1046}
2025-07-30 13:05:10,938 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851910, 缓存大小: 48/1000
2025-07-30 13:05:10,938 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:05:11,731 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851911, 'datetime': '2025-07-30 13:05:11', 'temperature': 22, 'humidity': 72, 'co2': 1048}
2025-07-30 13:05:11,731 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851911, 缓存大小: 49/1000
2025-07-30 13:05:11,731 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:05:12,958 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851912, 'datetime': '2025-07-30 13:05:12', 'temperature': 22, 'humidity': 54, 'co2': 679}
2025-07-30 13:05:12,959 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851912, 缓存大小: 50/1000
2025-07-30 13:05:12,959 - sensor_collector - INFO - 已将 50 条传感器数据提交到异步写入队列
2025-07-30 13:05:12,959 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:05:13,751 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851913, 'datetime': '2025-07-30 13:05:13', 'temperature': 25, 'humidity': 65, 'co2': 689}
2025-07-30 13:05:13,751 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851913, 缓存大小: 1/1000
2025-07-30 13:05:13,751 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:05:14,981 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851914, 'datetime': '2025-07-30 13:05:14', 'temperature': 34, 'humidity': 63, 'co2': 780}
2025-07-30 13:05:14,981 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851914, 缓存大小: 2/1000
2025-07-30 13:05:14,981 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:05:15,772 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851915, 'datetime': '2025-07-30 13:05:15', 'temperature': 23, 'humidity': 71, 'co2': 806}
2025-07-30 13:05:15,772 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851915, 缓存大小: 3/1000
2025-07-30 13:05:15,772 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:05:17,001 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851916, 'datetime': '2025-07-30 13:05:16', 'temperature': 32, 'humidity': 40, 'co2': 1044}
2025-07-30 13:05:17,001 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851916, 缓存大小: 4/1000
2025-07-30 13:05:17,001 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:05:17,794 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851917, 'datetime': '2025-07-30 13:05:17', 'temperature': 29, 'humidity': 40, 'co2': 1172}
2025-07-30 13:05:17,794 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851917, 缓存大小: 5/1000
2025-07-30 13:05:17,795 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:05:19,023 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851919, 'datetime': '2025-07-30 13:05:19', 'temperature': 31, 'humidity': 48, 'co2': 374}
2025-07-30 13:05:19,024 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851919, 缓存大小: 6/1000
2025-07-30 13:05:19,024 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:05:19,814 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851919, 'datetime': '2025-07-30 13:05:19', 'temperature': 31, 'humidity': 48, 'co2': 481}
2025-07-30 13:05:19,814 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:05:20,250 - sensor_collector - INFO - 已将 6 条传感器数据提交到异步写入队列
2025-07-30 13:05:21,046 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851921, 'datetime': '2025-07-30 13:05:21', 'temperature': 20, 'humidity': 78, 'co2': 850}
2025-07-30 13:05:21,046 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851921, 缓存大小: 1/1000
2025-07-30 13:05:21,046 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:05:21,835 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851921, 'datetime': '2025-07-30 13:05:21', 'temperature': 27, 'humidity': 53, 'co2': 981}
2025-07-30 13:05:21,836 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:05:23,067 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851923, 'datetime': '2025-07-30 13:05:23', 'temperature': 21, 'humidity': 70, 'co2': 353}
2025-07-30 13:05:23,068 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851923, 缓存大小: 2/1000
2025-07-30 13:05:23,068 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:05:23,858 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851923, 'datetime': '2025-07-30 13:05:23', 'temperature': 20, 'humidity': 57, 'co2': 872}
2025-07-30 13:05:23,858 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:05:25,090 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851925, 'datetime': '2025-07-30 13:05:25', 'temperature': 28, 'humidity': 61, 'co2': 441}
2025-07-30 13:05:25,091 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851925, 缓存大小: 3/1000
2025-07-30 13:05:25,091 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:05:25,879 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851925, 'datetime': '2025-07-30 13:05:25', 'temperature': 23, 'humidity': 52, 'co2': 922}
2025-07-30 13:05:25,879 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:05:27,112 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851927, 'datetime': '2025-07-30 13:05:27', 'temperature': 22, 'humidity': 46, 'co2': 609}
2025-07-30 13:05:27,113 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851927, 缓存大小: 4/1000
2025-07-30 13:05:27,113 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:05:29,134 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851929, 'datetime': '2025-07-30 13:05:29', 'temperature': 29, 'humidity': 60, 'co2': 560}
2025-07-30 13:05:29,135 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851929, 缓存大小: 5/1000
2025-07-30 13:05:29,135 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:05:31,155 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851931, 'datetime': '2025-07-30 13:05:31', 'temperature': 22, 'humidity': 67, 'co2': 499}
2025-07-30 13:05:31,155 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851931, 缓存大小: 6/1000
2025-07-30 13:05:31,156 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:05:33,178 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851933, 'datetime': '2025-07-30 13:05:33', 'temperature': 25, 'humidity': 77, 'co2': 404}
2025-07-30 13:05:33,178 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851933, 缓存大小: 7/1000
2025-07-30 13:05:33,179 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:05:35,199 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851935, 'datetime': '2025-07-30 13:05:35', 'temperature': 28, 'humidity': 71, 'co2': 639}
2025-07-30 13:05:35,200 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851935, 缓存大小: 8/1000
2025-07-30 13:05:35,200 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:05:37,221 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851937, 'datetime': '2025-07-30 13:05:37', 'temperature': 28, 'humidity': 57, 'co2': 996}
2025-07-30 13:05:37,221 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851937, 缓存大小: 9/1000
2025-07-30 13:05:37,221 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:05:50,349 - sensor_collector - INFO - --- 开始传感器数据采集周期 ---
2025-07-30 13:05:50,350 - sensor_collector - INFO - 开始采集传感器数据: 2025-07-30 13:05:50
2025-07-30 13:05:50,350 - sensor_collector - INFO - 开始采集温湿度CO2数据...
2025-07-30 13:05:50,351 - sensor_collector - INFO - 温湿度CO2数据采集成功
2025-07-30 13:05:50,351 - sensor_collector - INFO - 开始采集GPS数据...
2025-07-30 13:05:50,351 - sensor_collector - INFO - GPS数据采集成功
2025-07-30 13:05:50,351 - sensor_collector - INFO - 开始采集风速数据...
2025-07-30 13:05:50,351 - sensor_collector - INFO - 风速数据采集成功
2025-07-30 13:05:50,351 - sensor_collector - INFO - 成功采集到 6 项传感器数据
2025-07-30 13:05:50,352 - sensor_collector - INFO - 完成数据质量验证
2025-07-30 13:05:50,352 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851950, 缓存大小: 10/1000
2025-07-30 13:06:10,361 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851970, 'datetime': '2025-07-30 13:06:10', 'temperature': 23, 'humidity': 72, 'co2': 435}
2025-07-30 13:06:10,362 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851970, 缓存大小: 11/1000
2025-07-30 13:06:10,362 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:06:12,380 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851972, 'datetime': '2025-07-30 13:06:12', 'temperature': 20, 'humidity': 45, 'co2': 596}
2025-07-30 13:06:12,380 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851972, 缓存大小: 12/1000
2025-07-30 13:06:12,380 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:06:14,399 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851974, 'datetime': '2025-07-30 13:06:14', 'temperature': 26, 'humidity': 61, 'co2': 1087}
2025-07-30 13:06:14,399 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851974, 缓存大小: 13/1000
2025-07-30 13:06:14,399 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:06:15,218 - sensor_collector - INFO - 接收到测试数据注入: {'temperature': 22, 'humidity': 50, 'co2': 518, 'timestamp': 1753851975, 'datetime': '2025-07-30 13:06:15'}
2025-07-30 13:06:15,218 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851975, 缓存大小: 14/1000
2025-07-30 13:06:15,219 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:06:16,418 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851976, 'datetime': '2025-07-30 13:06:16', 'temperature': 32, 'humidity': 62, 'co2': 582}
2025-07-30 13:06:16,419 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851976, 缓存大小: 15/1000
2025-07-30 13:06:16,419 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:06:18,437 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851978, 'datetime': '2025-07-30 13:06:18', 'temperature': 23, 'humidity': 75, 'co2': 529}
2025-07-30 13:06:18,438 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851978, 缓存大小: 16/1000
2025-07-30 13:06:18,438 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:06:20,306 - sensor_collector - INFO - 已将 16 条传感器数据提交到异步写入队列
2025-07-30 13:06:20,456 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851980, 'datetime': '2025-07-30 13:06:20', 'temperature': 34, 'humidity': 61, 'co2': 706}
2025-07-30 13:06:20,456 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851980, 缓存大小: 1/1000
2025-07-30 13:06:20,457 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:06:22,475 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851982, 'datetime': '2025-07-30 13:06:22', 'temperature': 20, 'humidity': 56, 'co2': 1063}
2025-07-30 13:06:22,476 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851982, 缓存大小: 2/1000
2025-07-30 13:06:22,476 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:06:24,494 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851984, 'datetime': '2025-07-30 13:06:24', 'temperature': 25, 'humidity': 62, 'co2': 379}
2025-07-30 13:06:24,495 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851984, 缓存大小: 3/1000
2025-07-30 13:06:24,495 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:06:26,514 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851986, 'datetime': '2025-07-30 13:06:26', 'temperature': 22, 'humidity': 66, 'co2': 938}
2025-07-30 13:06:26,515 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851986, 缓存大小: 4/1000
2025-07-30 13:06:26,515 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:06:27,238 - sensor_collector - INFO - 接收到测试数据注入: {'temperature': 29, 'humidity': 54, 'co2': 661, 'timestamp': 1753851987, 'datetime': '2025-07-30 13:06:27'}
2025-07-30 13:06:27,239 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851987, 缓存大小: 5/1000
2025-07-30 13:06:27,239 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:06:28,534 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851988, 'datetime': '2025-07-30 13:06:28', 'temperature': 17, 'humidity': 72, 'co2': 1195}
2025-07-30 13:06:28,535 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851988, 缓存大小: 6/1000
2025-07-30 13:06:28,535 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:06:30,554 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851990, 'datetime': '2025-07-30 13:06:30', 'temperature': 20, 'humidity': 79, 'co2': 711}
2025-07-30 13:06:30,554 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851990, 缓存大小: 7/1000
2025-07-30 13:06:30,554 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:06:32,574 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851992, 'datetime': '2025-07-30 13:06:32', 'temperature': 21, 'humidity': 31, 'co2': 796}
2025-07-30 13:06:32,574 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851992, 缓存大小: 8/1000
2025-07-30 13:06:32,574 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:06:34,592 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851994, 'datetime': '2025-07-30 13:06:34', 'temperature': 25, 'humidity': 45, 'co2': 1123}
2025-07-30 13:06:34,593 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851994, 缓存大小: 9/1000
2025-07-30 13:06:34,593 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:06:36,612 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851996, 'datetime': '2025-07-30 13:06:36', 'temperature': 19, 'humidity': 58, 'co2': 1056}
2025-07-30 13:06:36,613 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851996, 缓存大小: 10/1000
2025-07-30 13:06:36,613 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:06:38,632 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753851998, 'datetime': '2025-07-30 13:06:38', 'temperature': 20, 'humidity': 44, 'co2': 703}
2025-07-30 13:06:38,632 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851998, 缓存大小: 11/1000
2025-07-30 13:06:38,632 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:06:39,258 - sensor_collector - INFO - 接收到测试数据注入: {'temperature': 23, 'humidity': 73, 'co2': 510, 'timestamp': 1753851999, 'datetime': '2025-07-30 13:06:39'}
2025-07-30 13:06:39,258 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753851999, 缓存大小: 12/1000
2025-07-30 13:06:39,258 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:06:40,652 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852000, 'datetime': '2025-07-30 13:06:40', 'temperature': 15, 'humidity': 62, 'co2': 1046}
2025-07-30 13:06:40,653 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852000, 缓存大小: 13/1000
2025-07-30 13:06:40,653 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:06:42,672 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852002, 'datetime': '2025-07-30 13:06:42', 'temperature': 18, 'humidity': 70, 'co2': 510}
2025-07-30 13:06:42,672 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852002, 缓存大小: 14/1000
2025-07-30 13:06:42,672 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:06:44,691 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852004, 'datetime': '2025-07-30 13:06:44', 'temperature': 33, 'humidity': 35, 'co2': 722}
2025-07-30 13:06:44,692 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852004, 缓存大小: 15/1000
2025-07-30 13:06:44,692 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:06:46,711 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852006, 'datetime': '2025-07-30 13:06:46', 'temperature': 18, 'humidity': 69, 'co2': 505}
2025-07-30 13:06:46,711 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852006, 缓存大小: 16/1000
2025-07-30 13:06:46,711 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:06:48,729 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852008, 'datetime': '2025-07-30 13:06:48', 'temperature': 32, 'humidity': 68, 'co2': 1170}
2025-07-30 13:06:48,730 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852008, 缓存大小: 17/1000
2025-07-30 13:06:48,730 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:06:50,412 - sensor_collector - INFO - --- 开始传感器数据采集周期 ---
2025-07-30 13:06:50,412 - sensor_collector - INFO - 开始采集传感器数据: 2025-07-30 13:06:50
2025-07-30 13:06:50,413 - sensor_collector - INFO - 开始采集温湿度CO2数据...
2025-07-30 13:06:50,413 - sensor_collector - INFO - 温湿度CO2数据采集成功
2025-07-30 13:06:50,413 - sensor_collector - INFO - 开始采集GPS数据...
2025-07-30 13:06:50,413 - sensor_collector - INFO - GPS数据采集成功
2025-07-30 13:06:50,413 - sensor_collector - INFO - 开始采集风速数据...
2025-07-30 13:06:50,414 - sensor_collector - INFO - 风速数据采集成功
2025-07-30 13:06:50,414 - sensor_collector - INFO - 成功采集到 6 项传感器数据
2025-07-30 13:06:50,414 - sensor_collector - INFO - 完成数据质量验证
2025-07-30 13:06:50,414 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852010, 缓存大小: 18/1000
2025-07-30 13:06:50,748 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852010, 'datetime': '2025-07-30 13:06:50', 'temperature': 26, 'humidity': 77, 'co2': 1164}
2025-07-30 13:06:50,749 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:06:51,277 - sensor_collector - INFO - 接收到测试数据注入: {'temperature': 21, 'humidity': 61, 'co2': 696, 'timestamp': 1753852011, 'datetime': '2025-07-30 13:06:51'}
2025-07-30 13:06:51,277 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852011, 缓存大小: 19/1000
2025-07-30 13:06:51,277 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:06:52,767 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852012, 'datetime': '2025-07-30 13:06:52', 'temperature': 34, 'humidity': 64, 'co2': 437}
2025-07-30 13:06:52,768 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852012, 缓存大小: 20/1000
2025-07-30 13:06:52,768 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:06:54,787 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852014, 'datetime': '2025-07-30 13:06:54', 'temperature': 20, 'humidity': 60, 'co2': 545}
2025-07-30 13:06:54,787 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852014, 缓存大小: 21/1000
2025-07-30 13:06:54,787 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:06:56,807 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852016, 'datetime': '2025-07-30 13:06:56', 'temperature': 23, 'humidity': 71, 'co2': 750}
2025-07-30 13:06:56,807 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852016, 缓存大小: 22/1000
2025-07-30 13:06:56,807 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:06:58,825 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852018, 'datetime': '2025-07-30 13:06:58', 'temperature': 27, 'humidity': 77, 'co2': 794}
2025-07-30 13:06:58,825 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852018, 缓存大小: 23/1000
2025-07-30 13:06:58,825 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:07:00,844 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852020, 'datetime': '2025-07-30 13:07:00', 'temperature': 19, 'humidity': 46, 'co2': 414}
2025-07-30 13:07:00,845 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852020, 缓存大小: 24/1000
2025-07-30 13:07:00,845 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:07:02,864 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852022, 'datetime': '2025-07-30 13:07:02', 'temperature': 19, 'humidity': 70, 'co2': 577}
2025-07-30 13:07:02,865 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852022, 缓存大小: 25/1000
2025-07-30 13:07:02,865 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:07:03,297 - sensor_collector - INFO - 接收到测试数据注入: {'temperature': 26, 'humidity': 51, 'co2': 716, 'timestamp': 1753852023, 'datetime': '2025-07-30 13:07:03'}
2025-07-30 13:07:03,298 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852023, 缓存大小: 26/1000
2025-07-30 13:07:03,298 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:07:04,884 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852024, 'datetime': '2025-07-30 13:07:04', 'temperature': 32, 'humidity': 43, 'co2': 1002}
2025-07-30 13:07:04,885 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852024, 缓存大小: 27/1000
2025-07-30 13:07:04,885 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:07:06,903 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852026, 'datetime': '2025-07-30 13:07:06', 'temperature': 29, 'humidity': 70, 'co2': 513}
2025-07-30 13:07:06,903 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852026, 缓存大小: 28/1000
2025-07-30 13:07:06,903 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:07:08,922 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852028, 'datetime': '2025-07-30 13:07:08', 'temperature': 16, 'humidity': 47, 'co2': 1013}
2025-07-30 13:07:08,922 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852028, 缓存大小: 29/1000
2025-07-30 13:07:08,922 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:07:10,941 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852030, 'datetime': '2025-07-30 13:07:10', 'temperature': 31, 'humidity': 60, 'co2': 394}
2025-07-30 13:07:10,941 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852030, 缓存大小: 30/1000
2025-07-30 13:07:10,941 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:07:12,962 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852032, 'datetime': '2025-07-30 13:07:12', 'temperature': 15, 'humidity': 40, 'co2': 993}
2025-07-30 13:07:12,962 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852032, 缓存大小: 31/1000
2025-07-30 13:07:12,963 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:07:14,981 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852034, 'datetime': '2025-07-30 13:07:14', 'temperature': 27, 'humidity': 52, 'co2': 1128}
2025-07-30 13:07:14,981 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852034, 缓存大小: 32/1000
2025-07-30 13:07:14,982 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:07:15,317 - sensor_collector - INFO - 接收到测试数据注入: {'temperature': 24, 'humidity': 59, 'co2': 990, 'timestamp': 1753852035, 'datetime': '2025-07-30 13:07:15'}
2025-07-30 13:07:15,317 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852035, 缓存大小: 33/1000
2025-07-30 13:07:15,317 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:07:17,000 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852037, 'datetime': '2025-07-30 13:07:17', 'temperature': 23, 'humidity': 55, 'co2': 364}
2025-07-30 13:07:17,001 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852037, 缓存大小: 34/1000
2025-07-30 13:07:17,001 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:07:19,020 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852039, 'datetime': '2025-07-30 13:07:19', 'temperature': 34, 'humidity': 46, 'co2': 818}
2025-07-30 13:07:19,020 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852039, 缓存大小: 35/1000
2025-07-30 13:07:19,020 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:07:20,362 - sensor_collector - INFO - 已将 35 条传感器数据提交到异步写入队列
2025-07-30 13:07:21,041 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852041, 'datetime': '2025-07-30 13:07:21', 'temperature': 25, 'humidity': 49, 'co2': 551}
2025-07-30 13:07:21,041 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852041, 缓存大小: 1/1000
2025-07-30 13:07:21,041 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:07:23,059 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852043, 'datetime': '2025-07-30 13:07:23', 'temperature': 30, 'humidity': 44, 'co2': 794}
2025-07-30 13:07:23,059 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852043, 缓存大小: 2/1000
2025-07-30 13:07:23,059 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:07:25,078 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852045, 'datetime': '2025-07-30 13:07:25', 'temperature': 28, 'humidity': 61, 'co2': 1110}
2025-07-30 13:07:25,078 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852045, 缓存大小: 3/1000
2025-07-30 13:07:25,078 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:07:27,096 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852047, 'datetime': '2025-07-30 13:07:27', 'temperature': 16, 'humidity': 62, 'co2': 1055}
2025-07-30 13:07:27,097 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852047, 缓存大小: 4/1000
2025-07-30 13:07:27,097 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:07:27,336 - sensor_collector - INFO - 接收到测试数据注入: {'temperature': 23, 'humidity': 51, 'co2': 411, 'timestamp': 1753852047, 'datetime': '2025-07-30 13:07:27'}
2025-07-30 13:07:27,336 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:07:29,115 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852049, 'datetime': '2025-07-30 13:07:29', 'temperature': 24, 'humidity': 77, 'co2': 784}
2025-07-30 13:07:29,115 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852049, 缓存大小: 5/1000
2025-07-30 13:07:29,115 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:07:31,133 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852051, 'datetime': '2025-07-30 13:07:31', 'temperature': 31, 'humidity': 79, 'co2': 834}
2025-07-30 13:07:31,134 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852051, 缓存大小: 6/1000
2025-07-30 13:07:31,134 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:07:33,153 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852053, 'datetime': '2025-07-30 13:07:33', 'temperature': 29, 'humidity': 54, 'co2': 346}
2025-07-30 13:07:33,154 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852053, 缓存大小: 7/1000
2025-07-30 13:07:33,154 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:07:35,172 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852055, 'datetime': '2025-07-30 13:07:35', 'temperature': 25, 'humidity': 35, 'co2': 473}
2025-07-30 13:07:35,172 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852055, 缓存大小: 8/1000
2025-07-30 13:07:35,173 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:07:37,191 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852057, 'datetime': '2025-07-30 13:07:37', 'temperature': 27, 'humidity': 32, 'co2': 550}
2025-07-30 13:07:37,191 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852057, 缓存大小: 9/1000
2025-07-30 13:07:37,191 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:07:39,210 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852059, 'datetime': '2025-07-30 13:07:39', 'temperature': 18, 'humidity': 60, 'co2': 407}
2025-07-30 13:07:39,211 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852059, 缓存大小: 10/1000
2025-07-30 13:07:39,211 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:07:39,355 - sensor_collector - INFO - 接收到测试数据注入: {'temperature': 29, 'humidity': 73, 'co2': 860, 'timestamp': 1753852059, 'datetime': '2025-07-30 13:07:39'}
2025-07-30 13:07:39,356 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:07:41,230 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852061, 'datetime': '2025-07-30 13:07:41', 'temperature': 27, 'humidity': 68, 'co2': 650}
2025-07-30 13:07:41,231 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852061, 缓存大小: 11/1000
2025-07-30 13:07:41,231 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:07:43,248 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852063, 'datetime': '2025-07-30 13:07:43', 'temperature': 33, 'humidity': 72, 'co2': 996}
2025-07-30 13:07:43,249 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852063, 缓存大小: 12/1000
2025-07-30 13:07:43,249 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:07:45,268 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852065, 'datetime': '2025-07-30 13:07:45', 'temperature': 19, 'humidity': 62, 'co2': 858}
2025-07-30 13:07:45,268 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852065, 缓存大小: 13/1000
2025-07-30 13:07:45,268 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:07:47,286 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852067, 'datetime': '2025-07-30 13:07:47', 'temperature': 25, 'humidity': 43, 'co2': 646}
2025-07-30 13:07:47,287 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852067, 缓存大小: 14/1000
2025-07-30 13:07:47,287 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:07:49,305 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852069, 'datetime': '2025-07-30 13:07:49', 'temperature': 24, 'humidity': 30, 'co2': 376}
2025-07-30 13:07:49,306 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852069, 缓存大小: 15/1000
2025-07-30 13:07:49,306 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:07:50,475 - sensor_collector - INFO - --- 开始传感器数据采集周期 ---
2025-07-30 13:07:50,475 - sensor_collector - INFO - 开始采集传感器数据: 2025-07-30 13:07:50
2025-07-30 13:07:50,476 - sensor_collector - INFO - 开始采集温湿度CO2数据...
2025-07-30 13:07:50,476 - sensor_collector - INFO - 温湿度CO2数据采集成功
2025-07-30 13:07:50,476 - sensor_collector - INFO - 开始采集GPS数据...
2025-07-30 13:07:50,476 - sensor_collector - INFO - GPS数据采集成功
2025-07-30 13:07:50,476 - sensor_collector - INFO - 开始采集风速数据...
2025-07-30 13:07:50,477 - sensor_collector - INFO - 风速数据采集成功
2025-07-30 13:07:50,477 - sensor_collector - INFO - 成功采集到 6 项传感器数据
2025-07-30 13:07:50,477 - sensor_collector - INFO - 完成数据质量验证
2025-07-30 13:07:50,477 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852070, 缓存大小: 16/1000
2025-07-30 13:07:51,325 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852071, 'datetime': '2025-07-30 13:07:51', 'temperature': 28, 'humidity': 42, 'co2': 897}
2025-07-30 13:07:51,325 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852071, 缓存大小: 17/1000
2025-07-30 13:07:51,325 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:07:51,374 - sensor_collector - INFO - 接收到测试数据注入: {'temperature': 29, 'humidity': 68, 'co2': 753, 'timestamp': 1753852071, 'datetime': '2025-07-30 13:07:51'}
2025-07-30 13:07:51,375 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:07:53,343 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852073, 'datetime': '2025-07-30 13:07:53', 'temperature': 23, 'humidity': 30, 'co2': 968}
2025-07-30 13:07:53,343 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852073, 缓存大小: 18/1000
2025-07-30 13:07:53,344 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:07:55,362 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852075, 'datetime': '2025-07-30 13:07:55', 'temperature': 23, 'humidity': 36, 'co2': 848}
2025-07-30 13:07:55,363 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852075, 缓存大小: 19/1000
2025-07-30 13:07:55,363 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:07:57,381 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852077, 'datetime': '2025-07-30 13:07:57', 'temperature': 16, 'humidity': 53, 'co2': 487}
2025-07-30 13:07:57,381 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852077, 缓存大小: 20/1000
2025-07-30 13:07:57,382 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:07:59,399 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852079, 'datetime': '2025-07-30 13:07:59', 'temperature': 26, 'humidity': 42, 'co2': 907}
2025-07-30 13:07:59,399 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852079, 缓存大小: 21/1000
2025-07-30 13:07:59,399 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:08:01,417 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852081, 'datetime': '2025-07-30 13:08:01', 'temperature': 15, 'humidity': 38, 'co2': 488}
2025-07-30 13:08:01,418 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852081, 缓存大小: 22/1000
2025-07-30 13:08:01,418 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:08:03,392 - sensor_collector - INFO - 接收到测试数据注入: {'temperature': 21, 'humidity': 53, 'co2': 775, 'timestamp': 1753852083, 'datetime': '2025-07-30 13:08:03'}
2025-07-30 13:08:03,392 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852083, 缓存大小: 23/1000
2025-07-30 13:08:03,393 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:08:03,437 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852083, 'datetime': '2025-07-30 13:08:03', 'temperature': 17, 'humidity': 44, 'co2': 1033}
2025-07-30 13:08:03,437 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:08:05,458 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852085, 'datetime': '2025-07-30 13:08:05', 'temperature': 31, 'humidity': 71, 'co2': 841}
2025-07-30 13:08:05,458 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852085, 缓存大小: 24/1000
2025-07-30 13:08:05,458 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:08:07,477 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852087, 'datetime': '2025-07-30 13:08:07', 'temperature': 22, 'humidity': 47, 'co2': 1007}
2025-07-30 13:08:07,478 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852087, 缓存大小: 25/1000
2025-07-30 13:08:07,478 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:08:09,497 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852089, 'datetime': '2025-07-30 13:08:09', 'temperature': 24, 'humidity': 54, 'co2': 475}
2025-07-30 13:08:09,498 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852089, 缓存大小: 26/1000
2025-07-30 13:08:09,498 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:08:20,418 - sensor_collector - INFO - 已将 26 条传感器数据提交到异步写入队列
2025-07-30 13:08:50,539 - sensor_collector - INFO - --- 开始传感器数据采集周期 ---
2025-07-30 13:08:50,539 - sensor_collector - INFO - 开始采集传感器数据: 2025-07-30 13:08:50
2025-07-30 13:08:50,540 - sensor_collector - INFO - 开始采集温湿度CO2数据...
2025-07-30 13:08:50,540 - sensor_collector - INFO - 温湿度CO2数据采集成功
2025-07-30 13:08:50,540 - sensor_collector - INFO - 开始采集GPS数据...
2025-07-30 13:08:50,540 - sensor_collector - INFO - GPS数据采集成功
2025-07-30 13:08:50,540 - sensor_collector - INFO - 开始采集风速数据...
2025-07-30 13:08:50,541 - sensor_collector - INFO - 风速数据采集成功
2025-07-30 13:08:50,541 - sensor_collector - INFO - 成功采集到 6 项传感器数据
2025-07-30 13:08:50,541 - sensor_collector - INFO - 完成数据质量验证
2025-07-30 13:08:50,541 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852130, 缓存大小: 1/1000
2025-07-30 13:09:20,474 - sensor_collector - INFO - 已将 1 条传感器数据提交到异步写入队列
2025-07-30 13:09:50,600 - sensor_collector - INFO - --- 开始传感器数据采集周期 ---
2025-07-30 13:09:50,601 - sensor_collector - INFO - 开始采集传感器数据: 2025-07-30 13:09:50
2025-07-30 13:09:50,601 - sensor_collector - INFO - 开始采集温湿度CO2数据...
2025-07-30 13:09:50,601 - sensor_collector - INFO - 温湿度CO2数据采集成功
2025-07-30 13:09:50,602 - sensor_collector - INFO - 开始采集GPS数据...
2025-07-30 13:09:50,602 - sensor_collector - INFO - GPS数据采集成功
2025-07-30 13:09:50,602 - sensor_collector - INFO - 开始采集风速数据...
2025-07-30 13:09:50,602 - sensor_collector - INFO - 风速数据采集成功
2025-07-30 13:09:50,602 - sensor_collector - INFO - 成功采集到 6 项传感器数据
2025-07-30 13:09:50,603 - sensor_collector - INFO - 完成数据质量验证
2025-07-30 13:09:50,603 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852190, 缓存大小: 1/1000
2025-07-30 13:10:06,572 - sensor_collector - INFO - 接收到测试数据注入: {'temperature': 21, 'humidity': 73, 'co2': 678, 'timestamp': 1753852206, 'datetime': '2025-07-30 13:10:06'}
2025-07-30 13:10:06,573 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852206, 缓存大小: 2/1000
2025-07-30 13:10:06,573 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:10:10,311 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852210, 'datetime': '2025-07-30 13:10:10', 'temperature': 25, 'humidity': 63, 'co2': 936}
2025-07-30 13:10:10,311 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852210, 缓存大小: 3/1000
2025-07-30 13:10:10,311 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:10:12,329 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852212, 'datetime': '2025-07-30 13:10:12', 'temperature': 26, 'humidity': 73, 'co2': 1005}
2025-07-30 13:10:12,329 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852212, 缓存大小: 4/1000
2025-07-30 13:10:12,329 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:10:14,347 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852214, 'datetime': '2025-07-30 13:10:14', 'temperature': 30, 'humidity': 56, 'co2': 1195}
2025-07-30 13:10:14,347 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852214, 缓存大小: 5/1000
2025-07-30 13:10:14,347 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:10:16,366 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852216, 'datetime': '2025-07-30 13:10:16', 'temperature': 32, 'humidity': 49, 'co2': 1040}
2025-07-30 13:10:16,366 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852216, 缓存大小: 6/1000
2025-07-30 13:10:16,367 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:10:18,385 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852218, 'datetime': '2025-07-30 13:10:18', 'temperature': 28, 'humidity': 73, 'co2': 1074}
2025-07-30 13:10:18,385 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852218, 缓存大小: 7/1000
2025-07-30 13:10:18,385 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:10:18,591 - sensor_collector - INFO - 接收到测试数据注入: {'temperature': 27, 'humidity': 55, 'co2': 756, 'timestamp': 1753852218, 'datetime': '2025-07-30 13:10:18'}
2025-07-30 13:10:18,592 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:10:20,403 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852220, 'datetime': '2025-07-30 13:10:20', 'temperature': 34, 'humidity': 72, 'co2': 1183}
2025-07-30 13:10:20,404 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852220, 缓存大小: 8/1000
2025-07-30 13:10:20,404 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:10:20,530 - sensor_collector - INFO - 已将 8 条传感器数据提交到异步写入队列
2025-07-30 13:10:22,423 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852222, 'datetime': '2025-07-30 13:10:22', 'temperature': 20, 'humidity': 61, 'co2': 659}
2025-07-30 13:10:22,424 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852222, 缓存大小: 1/1000
2025-07-30 13:10:22,424 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:10:24,442 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852224, 'datetime': '2025-07-30 13:10:24', 'temperature': 32, 'humidity': 70, 'co2': 554}
2025-07-30 13:10:24,443 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852224, 缓存大小: 2/1000
2025-07-30 13:10:24,443 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:10:26,463 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852226, 'datetime': '2025-07-30 13:10:26', 'temperature': 18, 'humidity': 56, 'co2': 588}
2025-07-30 13:10:26,463 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852226, 缓存大小: 3/1000
2025-07-30 13:10:26,463 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:10:28,481 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852228, 'datetime': '2025-07-30 13:10:28', 'temperature': 26, 'humidity': 64, 'co2': 810}
2025-07-30 13:10:28,481 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852228, 缓存大小: 4/1000
2025-07-30 13:10:28,482 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:10:30,501 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852230, 'datetime': '2025-07-30 13:10:30', 'temperature': 34, 'humidity': 40, 'co2': 1166}
2025-07-30 13:10:30,501 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852230, 缓存大小: 5/1000
2025-07-30 13:10:30,501 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:10:30,611 - sensor_collector - INFO - 接收到测试数据注入: {'temperature': 26, 'humidity': 78, 'co2': 722, 'timestamp': 1753852230, 'datetime': '2025-07-30 13:10:30'}
2025-07-30 13:10:30,611 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:10:32,519 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852232, 'datetime': '2025-07-30 13:10:32', 'temperature': 29, 'humidity': 43, 'co2': 768}
2025-07-30 13:10:32,519 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852232, 缓存大小: 6/1000
2025-07-30 13:10:32,519 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:10:34,538 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852234, 'datetime': '2025-07-30 13:10:34', 'temperature': 28, 'humidity': 70, 'co2': 411}
2025-07-30 13:10:34,538 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852234, 缓存大小: 7/1000
2025-07-30 13:10:34,538 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:10:36,557 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852236, 'datetime': '2025-07-30 13:10:36', 'temperature': 23, 'humidity': 52, 'co2': 663}
2025-07-30 13:10:36,557 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852236, 缓存大小: 8/1000
2025-07-30 13:10:36,557 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:10:38,576 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852238, 'datetime': '2025-07-30 13:10:38', 'temperature': 30, 'humidity': 61, 'co2': 704}
2025-07-30 13:10:38,577 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852238, 缓存大小: 9/1000
2025-07-30 13:10:38,577 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:10:40,594 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852240, 'datetime': '2025-07-30 13:10:40', 'temperature': 24, 'humidity': 37, 'co2': 781}
2025-07-30 13:10:40,595 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852240, 缓存大小: 10/1000
2025-07-30 13:10:40,595 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:10:42,614 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852242, 'datetime': '2025-07-30 13:10:42', 'temperature': 20, 'humidity': 64, 'co2': 866}
2025-07-30 13:10:42,614 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852242, 缓存大小: 11/1000
2025-07-30 13:10:42,614 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:10:42,629 - sensor_collector - INFO - 接收到测试数据注入: {'temperature': 27, 'humidity': 60, 'co2': 832, 'timestamp': 1753852242, 'datetime': '2025-07-30 13:10:42'}
2025-07-30 13:10:42,630 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:10:44,632 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852244, 'datetime': '2025-07-30 13:10:44', 'temperature': 25, 'humidity': 66, 'co2': 1092}
2025-07-30 13:10:44,632 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852244, 缓存大小: 12/1000
2025-07-30 13:10:44,632 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:10:46,652 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852246, 'datetime': '2025-07-30 13:10:46', 'temperature': 23, 'humidity': 30, 'co2': 545}
2025-07-30 13:10:46,652 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852246, 缓存大小: 13/1000
2025-07-30 13:10:46,652 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:10:48,671 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852248, 'datetime': '2025-07-30 13:10:48', 'temperature': 24, 'humidity': 77, 'co2': 928}
2025-07-30 13:10:48,672 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852248, 缓存大小: 14/1000
2025-07-30 13:10:48,672 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:10:50,663 - sensor_collector - INFO - --- 开始传感器数据采集周期 ---
2025-07-30 13:10:50,663 - sensor_collector - INFO - 开始采集传感器数据: 2025-07-30 13:10:50
2025-07-30 13:10:50,664 - sensor_collector - INFO - 开始采集温湿度CO2数据...
2025-07-30 13:10:50,664 - sensor_collector - INFO - 温湿度CO2数据采集成功
2025-07-30 13:10:50,664 - sensor_collector - INFO - 开始采集GPS数据...
2025-07-30 13:10:50,664 - sensor_collector - INFO - GPS数据采集成功
2025-07-30 13:10:50,664 - sensor_collector - INFO - 开始采集风速数据...
2025-07-30 13:10:50,665 - sensor_collector - INFO - 风速数据采集成功
2025-07-30 13:10:50,665 - sensor_collector - INFO - 成功采集到 6 项传感器数据
2025-07-30 13:10:50,665 - sensor_collector - INFO - 完成数据质量验证
2025-07-30 13:10:50,665 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852250, 缓存大小: 15/1000
2025-07-30 13:10:50,691 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852250, 'datetime': '2025-07-30 13:10:50', 'temperature': 32, 'humidity': 57, 'co2': 518}
2025-07-30 13:10:50,691 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:10:52,711 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852252, 'datetime': '2025-07-30 13:10:52', 'temperature': 18, 'humidity': 54, 'co2': 324}
2025-07-30 13:10:52,711 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852252, 缓存大小: 16/1000
2025-07-30 13:10:52,711 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:10:54,649 - sensor_collector - INFO - 接收到测试数据注入: {'temperature': 23, 'humidity': 63, 'co2': 989, 'timestamp': 1753852254, 'datetime': '2025-07-30 13:10:54'}
2025-07-30 13:10:54,649 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852254, 缓存大小: 17/1000
2025-07-30 13:10:54,649 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:10:54,729 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852254, 'datetime': '2025-07-30 13:10:54', 'temperature': 23, 'humidity': 42, 'co2': 932}
2025-07-30 13:10:54,729 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:10:56,747 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852256, 'datetime': '2025-07-30 13:10:56', 'temperature': 32, 'humidity': 40, 'co2': 847}
2025-07-30 13:10:56,748 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852256, 缓存大小: 18/1000
2025-07-30 13:10:56,748 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:10:58,767 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852258, 'datetime': '2025-07-30 13:10:58', 'temperature': 16, 'humidity': 70, 'co2': 573}
2025-07-30 13:10:58,768 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852258, 缓存大小: 19/1000
2025-07-30 13:10:58,768 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:11:00,787 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852260, 'datetime': '2025-07-30 13:11:00', 'temperature': 24, 'humidity': 34, 'co2': 663}
2025-07-30 13:11:00,787 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852260, 缓存大小: 20/1000
2025-07-30 13:11:00,788 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:11:02,806 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852262, 'datetime': '2025-07-30 13:11:02', 'temperature': 34, 'humidity': 57, 'co2': 906}
2025-07-30 13:11:02,807 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852262, 缓存大小: 21/1000
2025-07-30 13:11:02,807 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:11:04,825 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852264, 'datetime': '2025-07-30 13:11:04', 'temperature': 31, 'humidity': 51, 'co2': 523}
2025-07-30 13:11:04,826 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852264, 缓存大小: 22/1000
2025-07-30 13:11:04,826 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:11:06,669 - sensor_collector - INFO - 接收到测试数据注入: {'temperature': 20, 'humidity': 68, 'co2': 676, 'timestamp': 1753852266, 'datetime': '2025-07-30 13:11:06'}
2025-07-30 13:11:06,669 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852266, 缓存大小: 23/1000
2025-07-30 13:11:06,669 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:11:06,844 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852266, 'datetime': '2025-07-30 13:11:06', 'temperature': 30, 'humidity': 48, 'co2': 926}
2025-07-30 13:11:06,845 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:11:08,863 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852268, 'datetime': '2025-07-30 13:11:08', 'temperature': 31, 'humidity': 44, 'co2': 1118}
2025-07-30 13:11:08,863 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852268, 缓存大小: 24/1000
2025-07-30 13:11:08,863 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:11:10,882 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852270, 'datetime': '2025-07-30 13:11:10', 'temperature': 33, 'humidity': 40, 'co2': 828}
2025-07-30 13:11:10,882 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852270, 缓存大小: 25/1000
2025-07-30 13:11:10,882 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:11:12,902 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852272, 'datetime': '2025-07-30 13:11:12', 'temperature': 30, 'humidity': 63, 'co2': 908}
2025-07-30 13:11:12,902 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852272, 缓存大小: 26/1000
2025-07-30 13:11:12,902 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:11:14,920 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852274, 'datetime': '2025-07-30 13:11:14', 'temperature': 17, 'humidity': 47, 'co2': 581}
2025-07-30 13:11:14,920 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852274, 缓存大小: 27/1000
2025-07-30 13:11:14,921 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:11:16,939 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852276, 'datetime': '2025-07-30 13:11:16', 'temperature': 24, 'humidity': 74, 'co2': 1117}
2025-07-30 13:11:16,940 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852276, 缓存大小: 28/1000
2025-07-30 13:11:16,940 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:11:18,688 - sensor_collector - INFO - 接收到测试数据注入: {'temperature': 26, 'humidity': 57, 'co2': 644, 'timestamp': 1753852278, 'datetime': '2025-07-30 13:11:18'}
2025-07-30 13:11:18,689 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852278, 缓存大小: 29/1000
2025-07-30 13:11:18,689 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:11:18,958 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852278, 'datetime': '2025-07-30 13:11:18', 'temperature': 26, 'humidity': 45, 'co2': 796}
2025-07-30 13:11:18,959 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:11:20,578 - sensor_collector - INFO - 已将 29 条传感器数据提交到异步写入队列
2025-07-30 13:11:20,977 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852280, 'datetime': '2025-07-30 13:11:20', 'temperature': 33, 'humidity': 48, 'co2': 326}
2025-07-30 13:11:20,977 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852280, 缓存大小: 1/1000
2025-07-30 13:11:20,978 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:11:22,996 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852282, 'datetime': '2025-07-30 13:11:22', 'temperature': 30, 'humidity': 63, 'co2': 779}
2025-07-30 13:11:22,996 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852282, 缓存大小: 2/1000
2025-07-30 13:11:22,997 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:11:25,016 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852285, 'datetime': '2025-07-30 13:11:25', 'temperature': 31, 'humidity': 41, 'co2': 1000}
2025-07-30 13:11:25,016 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852285, 缓存大小: 3/1000
2025-07-30 13:11:25,016 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:11:27,035 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852287, 'datetime': '2025-07-30 13:11:27', 'temperature': 28, 'humidity': 61, 'co2': 765}
2025-07-30 13:11:27,036 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852287, 缓存大小: 4/1000
2025-07-30 13:11:27,036 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:11:29,055 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852289, 'datetime': '2025-07-30 13:11:29', 'temperature': 17, 'humidity': 73, 'co2': 1001}
2025-07-30 13:11:29,055 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852289, 缓存大小: 5/1000
2025-07-30 13:11:29,055 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:11:30,707 - sensor_collector - INFO - 接收到测试数据注入: {'temperature': 27, 'humidity': 64, 'co2': 566, 'timestamp': 1753852290, 'datetime': '2025-07-30 13:11:30'}
2025-07-30 13:11:30,708 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852290, 缓存大小: 6/1000
2025-07-30 13:11:30,708 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:11:31,074 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852291, 'datetime': '2025-07-30 13:11:31', 'temperature': 29, 'humidity': 67, 'co2': 309}
2025-07-30 13:11:31,074 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852291, 缓存大小: 7/1000
2025-07-30 13:11:31,075 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:11:33,095 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852293, 'datetime': '2025-07-30 13:11:33', 'temperature': 25, 'humidity': 62, 'co2': 727}
2025-07-30 13:11:33,095 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852293, 缓存大小: 8/1000
2025-07-30 13:11:33,095 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:11:35,114 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852295, 'datetime': '2025-07-30 13:11:35', 'temperature': 28, 'humidity': 78, 'co2': 385}
2025-07-30 13:11:35,114 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852295, 缓存大小: 9/1000
2025-07-30 13:11:35,115 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:11:37,135 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852297, 'datetime': '2025-07-30 13:11:37', 'temperature': 16, 'humidity': 55, 'co2': 1039}
2025-07-30 13:11:37,135 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852297, 缓存大小: 10/1000
2025-07-30 13:11:37,135 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:11:39,156 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852299, 'datetime': '2025-07-30 13:11:39', 'temperature': 29, 'humidity': 63, 'co2': 841}
2025-07-30 13:11:39,156 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852299, 缓存大小: 11/1000
2025-07-30 13:11:39,156 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:11:41,175 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852301, 'datetime': '2025-07-30 13:11:41', 'temperature': 25, 'humidity': 45, 'co2': 378}
2025-07-30 13:11:41,176 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852301, 缓存大小: 12/1000
2025-07-30 13:11:41,176 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:11:42,726 - sensor_collector - INFO - 接收到测试数据注入: {'temperature': 22, 'humidity': 59, 'co2': 454, 'timestamp': 1753852302, 'datetime': '2025-07-30 13:11:42'}
2025-07-30 13:11:42,727 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852302, 缓存大小: 13/1000
2025-07-30 13:11:42,727 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:11:43,194 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852303, 'datetime': '2025-07-30 13:11:43', 'temperature': 34, 'humidity': 32, 'co2': 656}
2025-07-30 13:11:43,195 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852303, 缓存大小: 14/1000
2025-07-30 13:11:43,195 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:11:45,214 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852305, 'datetime': '2025-07-30 13:11:45', 'temperature': 30, 'humidity': 73, 'co2': 800}
2025-07-30 13:11:45,215 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852305, 缓存大小: 15/1000
2025-07-30 13:11:45,215 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:11:47,235 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852307, 'datetime': '2025-07-30 13:11:47', 'temperature': 21, 'humidity': 74, 'co2': 498}
2025-07-30 13:11:47,236 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852307, 缓存大小: 16/1000
2025-07-30 13:11:47,236 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:11:49,256 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852309, 'datetime': '2025-07-30 13:11:49', 'temperature': 26, 'humidity': 49, 'co2': 624}
2025-07-30 13:11:49,256 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852309, 缓存大小: 17/1000
2025-07-30 13:11:49,257 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:11:50,726 - sensor_collector - INFO - --- 开始传感器数据采集周期 ---
2025-07-30 13:11:50,726 - sensor_collector - INFO - 开始采集传感器数据: 2025-07-30 13:11:50
2025-07-30 13:11:50,726 - sensor_collector - INFO - 开始采集温湿度CO2数据...
2025-07-30 13:11:50,727 - sensor_collector - INFO - 温湿度CO2数据采集成功
2025-07-30 13:11:50,727 - sensor_collector - INFO - 开始采集GPS数据...
2025-07-30 13:11:50,727 - sensor_collector - INFO - GPS数据采集成功
2025-07-30 13:11:50,727 - sensor_collector - INFO - 开始采集风速数据...
2025-07-30 13:11:50,727 - sensor_collector - INFO - 风速数据采集成功
2025-07-30 13:11:50,727 - sensor_collector - INFO - 成功采集到 6 项传感器数据
2025-07-30 13:11:50,727 - sensor_collector - INFO - 完成数据质量验证
2025-07-30 13:11:50,728 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852310, 缓存大小: 18/1000
2025-07-30 13:11:51,276 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852311, 'datetime': '2025-07-30 13:11:51', 'temperature': 27, 'humidity': 54, 'co2': 553}
2025-07-30 13:11:51,277 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852311, 缓存大小: 19/1000
2025-07-30 13:11:51,277 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:11:53,295 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852313, 'datetime': '2025-07-30 13:11:53', 'temperature': 17, 'humidity': 31, 'co2': 1038}
2025-07-30 13:11:53,295 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852313, 缓存大小: 20/1000
2025-07-30 13:11:53,296 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:11:54,745 - sensor_collector - INFO - 接收到测试数据注入: {'temperature': 24, 'humidity': 70, 'co2': 889, 'timestamp': 1753852314, 'datetime': '2025-07-30 13:11:54'}
2025-07-30 13:11:54,745 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852314, 缓存大小: 21/1000
2025-07-30 13:11:54,745 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:11:55,314 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852315, 'datetime': '2025-07-30 13:11:55', 'temperature': 33, 'humidity': 76, 'co2': 924}
2025-07-30 13:11:55,315 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852315, 缓存大小: 22/1000
2025-07-30 13:11:55,315 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:11:57,334 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852317, 'datetime': '2025-07-30 13:11:57', 'temperature': 34, 'humidity': 65, 'co2': 458}
2025-07-30 13:11:57,334 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852317, 缓存大小: 23/1000
2025-07-30 13:11:57,334 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:11:59,353 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852319, 'datetime': '2025-07-30 13:11:59', 'temperature': 26, 'humidity': 46, 'co2': 622}
2025-07-30 13:11:59,354 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852319, 缓存大小: 24/1000
2025-07-30 13:11:59,354 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:12:01,373 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852321, 'datetime': '2025-07-30 13:12:01', 'temperature': 34, 'humidity': 50, 'co2': 1084}
2025-07-30 13:12:01,373 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852321, 缓存大小: 25/1000
2025-07-30 13:12:01,374 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:12:03,394 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852323, 'datetime': '2025-07-30 13:12:03', 'temperature': 20, 'humidity': 58, 'co2': 1024}
2025-07-30 13:12:03,394 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852323, 缓存大小: 26/1000
2025-07-30 13:12:03,394 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:12:05,413 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852325, 'datetime': '2025-07-30 13:12:05', 'temperature': 33, 'humidity': 70, 'co2': 443}
2025-07-30 13:12:05,413 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852325, 缓存大小: 27/1000
2025-07-30 13:12:05,414 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:12:07,432 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852327, 'datetime': '2025-07-30 13:12:07', 'temperature': 15, 'humidity': 41, 'co2': 1099}
2025-07-30 13:12:07,433 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852327, 缓存大小: 28/1000
2025-07-30 13:12:07,433 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:12:09,452 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753852329, 'datetime': '2025-07-30 13:12:09', 'temperature': 18, 'humidity': 55, 'co2': 648}
2025-07-30 13:12:09,452 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852329, 缓存大小: 29/1000
2025-07-30 13:12:09,453 - sensor_collector - INFO - 测试数据已保存
2025-07-30 13:12:20,636 - sensor_collector - INFO - 已将 29 条传感器数据提交到异步写入队列
2025-07-30 13:12:50,788 - sensor_collector - INFO - --- 开始传感器数据采集周期 ---
2025-07-30 13:12:50,789 - sensor_collector - INFO - 开始采集传感器数据: 2025-07-30 13:12:50
2025-07-30 13:12:50,789 - sensor_collector - INFO - 开始采集温湿度CO2数据...
2025-07-30 13:12:50,789 - sensor_collector - INFO - 温湿度CO2数据采集成功
2025-07-30 13:12:50,789 - sensor_collector - INFO - 开始采集GPS数据...
2025-07-30 13:12:50,789 - sensor_collector - INFO - GPS数据采集成功
2025-07-30 13:12:50,790 - sensor_collector - INFO - 开始采集风速数据...
2025-07-30 13:12:50,790 - sensor_collector - INFO - 风速数据采集成功
2025-07-30 13:12:50,790 - sensor_collector - INFO - 成功采集到 6 项传感器数据
2025-07-30 13:12:50,790 - sensor_collector - INFO - 完成数据质量验证
2025-07-30 13:12:50,791 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753852370, 缓存大小: 1/1000
2025-07-30 13:13:20,687 - sensor_collector - INFO - 已将 1 条传感器数据提交到异步写入队列
2025-07-30 13:13:33,571 - sensor_collector - INFO - 
正在停止传感器数据采集线程...
2025-07-30 13:13:33,571 - sensor_collector - INFO - 已取消传感器数据采集任务
2025-07-30 13:13:33,575 - sensor_collector - INFO - 已取消传感器缓存刷新定期任务
2025-07-30 13:13:33,575 - sensor_collector - INFO - 执行停止前的缓存刷新...
2025-07-30 13:13:33,575 - sensor_collector - INFO - 执行停止前的数据强制备份...
2025-07-30 13:13:33,589 - sensor_collector - INFO - 已创建数据备份: /home/<USER>/main_test/test_data/sensors/current/sensor_data.json.20250730131333.bak
2025-07-30 13:13:33,802 - sensor_collector - INFO - 🧪 模拟硬件控制器已停止
2025-07-30 13:13:33,803 - sensor_collector - INFO - 传感器数据采集线程已停止
