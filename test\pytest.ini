[tool:pytest]
# pytest配置文件

# 测试发现
testpaths = test
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# 输出配置
addopts = 
    -v
    --tb=short
    --strict-markers
    --strict-config
    --disable-warnings
    --color=yes
    --durations=10

# 标记配置
markers =
    unit: 单元测试
    integration: 集成测试
    performance: 性能测试
    stress: 压力测试
    api: API接口测试
    slow: 慢速测试（运行时间>5秒）
    hardware: 需要硬件设备的测试
    mqtt: 需要MQTT连接的测试
    serial: 需要串口设备的测试

# 过滤警告
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:paho.mqtt.*

# 最小版本要求
minversion = 6.0

# 测试超时（秒）
timeout = 300

# 并行测试配置
# 使用 pytest-xdist 插件时的配置
# addopts = -n auto

# 覆盖率配置（需要 pytest-cov 插件）
# addopts = --cov=. --cov-report=html --cov-report=term-missing

# 基准测试配置（需要 pytest-benchmark 插件）
# addopts = --benchmark-only --benchmark-sort=mean
