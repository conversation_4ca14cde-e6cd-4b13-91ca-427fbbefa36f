#!/bin/bash
echo "开始并发测试..."

# 并发发送10个请求
for i in {1..10}; do
  {
    curl -X POST http://192.168.0.111:5001/api/data/inject/sensor \
         -H "Content-Type: application/json" \
         -d "{\"co2\": $((400 + RANDOM % 200)), \"temperature\": $((20 + RANDOM % 15)), \"humidity\": $((50 + RANDOM % 30))}" \
         -w "请求$i: %{http_code} - %{time_total}s\n" \
         -s -o /dev/null
  } &
done

wait
echo "并发测试完成"
