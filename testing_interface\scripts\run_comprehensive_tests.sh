#!/bin/bash

# IoT传感器监控系统 - 综合测试脚本
# 自动执行所有测试功能，生成详细报告

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
BOARD_IP=${BOARD_IP:-"localhost"}
API_PORT=${API_PORT:-"5001"}
BASE_URL="http://${BOARD_IP}:${API_PORT}"
TEST_REPORT_DIR="test_data/reports"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
REPORT_FILE="${TEST_REPORT_DIR}/comprehensive_test_${TIMESTAMP}.log"

# 创建报告目录
mkdir -p "$TEST_REPORT_DIR"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1" | tee -a "$REPORT_FILE"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$REPORT_FILE"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$REPORT_FILE"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$REPORT_FILE"
}

# 测试函数
test_api_endpoint() {
    local method=$1
    local endpoint=$2
    local data=$3
    local expected_status=${4:-200}
    
    log_info "测试: $method $endpoint"
    
    if [ "$method" = "GET" ]; then
        response=$(curl -s -w "%{http_code}" -o /tmp/response.json "$BASE_URL$endpoint")
    else
        if [ -n "$data" ]; then
            response=$(curl -s -w "%{http_code}" -X "$method" \
                -H "Content-Type: application/json" \
                -d "$data" \
                -o /tmp/response.json \
                "$BASE_URL$endpoint")
        else
            response=$(curl -s -w "%{http_code}" -X "$method" \
                -o /tmp/response.json \
                "$BASE_URL$endpoint")
        fi
    fi
    
    http_code="${response: -3}"
    
    if [ "$http_code" -eq "$expected_status" ]; then
        log_success "✅ $endpoint - HTTP $http_code"
        return 0
    else
        log_error "❌ $endpoint - 期望 HTTP $expected_status, 实际 HTTP $http_code"
        cat /tmp/response.json | tee -a "$REPORT_FILE"
        return 1
    fi
}

# 等待服务启动
wait_for_service() {
    log_info "等待服务启动..."
    for i in {1..30}; do
        if curl -s "$BASE_URL/api/health" > /dev/null 2>&1; then
            log_success "服务已启动"
            return 0
        fi
        sleep 2
    done
    log_error "服务启动超时"
    return 1
}

# 主测试流程
main() {
    echo "========================================" | tee "$REPORT_FILE"
    echo "IoT传感器监控系统 - 综合测试报告" | tee -a "$REPORT_FILE"
    echo "测试时间: $(date)" | tee -a "$REPORT_FILE"
    echo "测试目标: $BASE_URL" | tee -a "$REPORT_FILE"
    echo "========================================" | tee -a "$REPORT_FILE"
    
    # 检查服务是否运行
    if ! wait_for_service; then
        log_error "无法连接到测试服务，请确保服务已启动："
        echo "python main.py --test-mode --config testing_interface/config/config_test.yaml --test-api --test-port 5001"
        exit 1
    fi
    
    local total_tests=0
    local passed_tests=0
    
    # 阶段1: 基础功能测试
    log_info "=== 阶段1: 基础功能测试 ==="
    
    # 健康检查
    if test_api_endpoint "GET" "/api/health"; then ((passed_tests++)); fi
    ((total_tests++))
    
    # 系统信息
    if test_api_endpoint "GET" "/api/info"; then ((passed_tests++)); fi
    ((total_tests++))
    
    # 系统状态
    if test_api_endpoint "GET" "/api/system/status"; then ((passed_tests++)); fi
    ((total_tests++))
    
    # 阶段2: 数据注入测试
    log_info "=== 阶段2: 数据注入测试 ==="
    
    # CO2数据注入
    co2_data='{"sensor_type": "co2", "value": 450, "unit": "ppm"}'
    if test_api_endpoint "POST" "/api/data/inject/sensor" "$co2_data"; then ((passed_tests++)); fi
    ((total_tests++))
    
    # 温度数据注入
    temp_data='{"sensor_type": "temperature", "value": 25.5, "unit": "°C"}'
    if test_api_endpoint "POST" "/api/data/inject/sensor" "$temp_data"; then ((passed_tests++)); fi
    ((total_tests++))
    
    # 湿度数据注入
    humidity_data='{"sensor_type": "humidity", "value": 65.2, "unit": "%"}'
    if test_api_endpoint "POST" "/api/data/inject/sensor" "$humidity_data"; then ((passed_tests++)); fi
    ((total_tests++))
    
    # 错误数据注入
    error_data='{"error_type": "sensor_error", "sensor_name": "co2_sensor", "error_message": "测试错误", "error_code": "TEST_001"}'
    if test_api_endpoint "POST" "/api/data/inject/error" "$error_data"; then ((passed_tests++)); fi
    ((total_tests++))
    
    # 批量数据注入
    batch_data='{"batch_id": "test_batch", "data": [{"sensor_type": "co2", "value": 420}, {"sensor_type": "temperature", "value": 24.8}]}'
    if test_api_endpoint "POST" "/api/data/inject/batch" "$batch_data"; then ((passed_tests++)); fi
    ((total_tests++))
    
    # 阶段3: 性能监控测试
    log_info "=== 阶段3: 性能监控测试 ==="
    
    # 启动监控
    monitor_data='{"interval": 2, "duration": 10}'
    if test_api_endpoint "POST" "/api/monitor/start" "$monitor_data"; then ((passed_tests++)); fi
    ((total_tests++))
    
    # 等待监控数据
    sleep 5
    
    # 获取监控数据
    if test_api_endpoint "GET" "/api/monitor/data?limit=5"; then ((passed_tests++)); fi
    ((total_tests++))
    
    # 停止监控
    if test_api_endpoint "POST" "/api/monitor/stop"; then ((passed_tests++)); fi
    ((total_tests++))
    
    # 阶段4: 模拟数据管理测试
    log_info "=== 阶段4: 模拟数据管理测试 ==="
    
    # 获取MQTT消息
    if test_api_endpoint "GET" "/api/mock/mqtt/messages?limit=10"; then ((passed_tests++)); fi
    ((total_tests++))
    
    # 模拟网络故障
    network_failure_data='{"duration": 5, "failure_rate": 0.5}'
    if test_api_endpoint "POST" "/api/mock/network/failure" "$network_failure_data"; then ((passed_tests++)); fi
    ((total_tests++))
    
    # 清空MQTT消息
    if test_api_endpoint "POST" "/api/mock/mqtt/clear"; then ((passed_tests++)); fi
    ((total_tests++))
    
    # 阶段5: 测试会话管理
    log_info "=== 阶段5: 测试会话管理 ==="
    
    # 开始测试会话
    session_data='{"name": "自动化测试会话", "description": "综合测试", "test_type": "automated"}'
    if test_api_endpoint "POST" "/api/test/session/start" "$session_data"; then ((passed_tests++)); fi
    ((total_tests++))
    
    # 获取会话状态
    if test_api_endpoint "GET" "/api/test/session/status"; then ((passed_tests++)); fi
    ((total_tests++))
    
    # 结束测试会话
    if test_api_endpoint "POST" "/api/test/session/stop"; then ((passed_tests++)); fi
    ((total_tests++))
    
    # 阶段6: 测试环境管理
    log_info "=== 阶段6: 测试环境管理 ==="
    
    # 获取环境状态
    if test_api_endpoint "GET" "/api/test/env/status"; then ((passed_tests++)); fi
    ((total_tests++))
    
    # 获取测试配置
    if test_api_endpoint "GET" "/api/test/env/config"; then ((passed_tests++)); fi
    ((total_tests++))
    
    # 阶段7: 错误处理测试
    log_info "=== 阶段7: 错误处理测试 ==="
    
    # 无效传感器类型 (应该返回400)
    invalid_data='{"sensor_type": "invalid_sensor", "value": 100}'
    if test_api_endpoint "POST" "/api/data/inject/sensor" "$invalid_data" 400; then ((passed_tests++)); fi
    ((total_tests++))
    
    # 缺失参数 (应该返回400)
    missing_data='{"value": 450}'
    if test_api_endpoint "POST" "/api/data/inject/sensor" "$missing_data" 400; then ((passed_tests++)); fi
    ((total_tests++))
    
    # 测试结果汇总
    echo "========================================" | tee -a "$REPORT_FILE"
    echo "测试结果汇总:" | tee -a "$REPORT_FILE"
    echo "总测试数: $total_tests" | tee -a "$REPORT_FILE"
    echo "通过测试: $passed_tests" | tee -a "$REPORT_FILE"
    echo "失败测试: $((total_tests - passed_tests))" | tee -a "$REPORT_FILE"
    
    success_rate=$(echo "scale=2; $passed_tests * 100 / $total_tests" | bc -l)
    echo "成功率: ${success_rate}%" | tee -a "$REPORT_FILE"
    
    if [ "$passed_tests" -eq "$total_tests" ]; then
        log_success "🎉 所有测试通过！"
        echo "========================================" | tee -a "$REPORT_FILE"
        return 0
    else
        log_warning "⚠️  部分测试失败，请检查详细日志"
        echo "========================================" | tee -a "$REPORT_FILE"
        return 1
    fi
}

# 清理函数
cleanup() {
    log_info "清理临时文件..."
    rm -f /tmp/response.json
}

# 设置清理陷阱
trap cleanup EXIT

# 检查依赖
if ! command -v curl &> /dev/null; then
    log_error "curl 未安装，请先安装 curl"
    exit 1
fi

if ! command -v bc &> /dev/null; then
    log_warning "bc 未安装，无法计算成功率"
fi

# 运行主测试
main "$@"
