"""
MQTT客户端单元测试

测试MQTT客户端的连接、发布、订阅等功能。
"""

import pytest
import time
import json
from unittest.mock import Mock, patch, MagicMock
from test.fixtures.mock_mqtt import MockMQTTClient, MockMQTTBroker
from test.fixtures.test_data import TestDataGenerator
from test.fixtures.config_samples import get_test_config


@pytest.mark.unit
class TestMQTTClient:
    """MQTT客户端测试类"""
    
    def setup_method(self):
        """测试前设置"""
        self.config = get_test_config("base")
        self.mock_logger = Mock()
        
    def test_mqtt_client_import(self):
        """测试MQTT客户端模块导入"""
        try:
            from communication.mqtt_client import MQTTClient
            assert MQTTClient is not None
        except ImportError as e:
            pytest.skip(f"MQTT客户端模块未找到: {e}")
    
    @patch('communication.mqtt_client.mqtt.Client')
    @patch('communication.mqtt_client.get_logger')
    def test_mqtt_client_initialization(self, mock_get_logger, mock_mqtt_client):
        """测试MQTT客户端初始化"""
        try:
            from communication.mqtt_client import MQTTClient
            
            mock_get_logger.return_value = self.mock_logger
            mock_client_instance = Mock()
            mock_mqtt_client.return_value = mock_client_instance
            
            mqtt_client = MQTTClient(self.config)
            
            assert mqtt_client is not None
            assert mqtt_client.config == self.config
            assert hasattr(mqtt_client, 'device_id')
            assert hasattr(mqtt_client, 'connected')
            
        except ImportError:
            pytest.skip("MQTT客户端模块未找到")
    
    @patch('communication.mqtt_client.mqtt.Client')
    @patch('communication.mqtt_client.get_logger')
    def test_mqtt_connection_success(self, mock_get_logger, mock_mqtt_client):
        """测试MQTT连接成功"""
        try:
            from communication.mqtt_client import MQTTClient
            
            mock_get_logger.return_value = self.mock_logger
            mock_client_instance = Mock()
            mock_client_instance.connect.return_value = 0  # 连接成功
            mock_mqtt_client.return_value = mock_client_instance
            
            mqtt_client = MQTTClient(self.config)
            result = mqtt_client.connect()
            
            assert result is True
            mock_client_instance.connect.assert_called_once()
            
        except ImportError:
            pytest.skip("MQTT客户端模块未找到")
    
    @patch('communication.mqtt_client.mqtt.Client')
    @patch('communication.mqtt_client.get_logger')
    def test_mqtt_connection_failure(self, mock_get_logger, mock_mqtt_client):
        """测试MQTT连接失败"""
        try:
            from communication.mqtt_client import MQTTClient
            
            mock_get_logger.return_value = self.mock_logger
            mock_client_instance = Mock()
            mock_client_instance.connect.return_value = 1  # 连接失败
            mock_mqtt_client.return_value = mock_client_instance
            
            mqtt_client = MQTTClient(self.config)
            result = mqtt_client.connect()
            
            assert result is False
            mock_client_instance.connect.assert_called_once()
            
        except ImportError:
            pytest.skip("MQTT客户端模块未找到")
    
    @patch('communication.mqtt_client.mqtt.Client')
    @patch('communication.mqtt_client.get_logger')
    def test_publish_sensor_data(self, mock_get_logger, mock_mqtt_client):
        """测试发布传感器数据"""
        try:
            from communication.mqtt_client import MQTTClient
            
            mock_get_logger.return_value = self.mock_logger
            mock_client_instance = Mock()
            mock_client_instance.publish.return_value.rc = 0  # 发布成功
            mock_mqtt_client.return_value = mock_client_instance
            
            mqtt_client = MQTTClient(self.config)
            mqtt_client.connected = True
            
            test_data = TestDataGenerator.generate_sensor_data()
            result = mqtt_client.publish_sensor_data(test_data)
            
            assert result is True
            mock_client_instance.publish.assert_called_once()
            
        except ImportError:
            pytest.skip("MQTT客户端模块未找到")
    
    @patch('communication.mqtt_client.mqtt.Client')
    @patch('communication.mqtt_client.get_logger')
    def test_publish_error_data(self, mock_get_logger, mock_mqtt_client):
        """测试发布错误数据"""
        try:
            from communication.mqtt_client import MQTTClient
            
            mock_get_logger.return_value = self.mock_logger
            mock_client_instance = Mock()
            mock_client_instance.publish.return_value.rc = 0
            mock_mqtt_client.return_value = mock_client_instance
            
            mqtt_client = MQTTClient(self.config)
            mqtt_client.connected = True
            
            error_data = TestDataGenerator.generate_error_data()
            result = mqtt_client.publish_error_data(error_data)
            
            assert result is True
            mock_client_instance.publish.assert_called_once()
            
        except ImportError:
            pytest.skip("MQTT客户端模块未找到")
    
    @patch('communication.mqtt_client.mqtt.Client')
    @patch('communication.mqtt_client.get_logger')
    def test_publish_when_disconnected(self, mock_get_logger, mock_mqtt_client):
        """测试断开连接时发布数据"""
        try:
            from communication.mqtt_client import MQTTClient
            
            mock_get_logger.return_value = self.mock_logger
            mock_client_instance = Mock()
            mock_mqtt_client.return_value = mock_client_instance
            
            mqtt_client = MQTTClient(self.config)
            mqtt_client.connected = False  # 未连接状态
            
            test_data = TestDataGenerator.generate_sensor_data()
            result = mqtt_client.publish_sensor_data(test_data)
            
            # 应该返回False或处理离线情况
            assert result is False or result is None
            
        except ImportError:
            pytest.skip("MQTT客户端模块未找到")
    
    def test_mock_mqtt_client_functionality(self):
        """测试Mock MQTT客户端功能"""
        # 创建Mock客户端
        mock_client = MockMQTTClient("TEST_DEVICE_001")
        
        # 测试连接
        assert mock_client.connect() is True
        assert mock_client.connected is True
        
        # 测试发布传感器数据
        test_data = TestDataGenerator.generate_sensor_data()
        result = mock_client.publish_sensor_data(test_data)
        assert result is True
        
        # 检查发布历史
        messages = mock_client.get_published_messages("sensor_data")
        assert len(messages) == 1
        assert messages[0]["data"] == test_data
        
        # 测试发布错误数据
        error_data = TestDataGenerator.generate_error_data()
        result = mock_client.publish_error_data(error_data)
        assert result is True
        
        # 测试断开连接
        mock_client.disconnect()
        assert mock_client.connected is False
    
    def test_mock_mqtt_client_error_simulation(self):
        """测试Mock MQTT客户端错误模拟"""
        mock_client = MockMQTTClient("TEST_DEVICE_001")
        mock_client.connect()
        
        # 设置50%成功率
        mock_client.set_publish_success_rate(0.5)
        
        # 多次发布，应该有成功和失败的情况
        results = []
        for _ in range(20):
            test_data = TestDataGenerator.generate_sensor_data()
            result = mock_client.publish_sensor_data(test_data)
            results.append(result)
        
        # 应该有成功和失败的情况
        assert True in results
        assert False in results
        
        # 恢复100%成功率
        mock_client.set_publish_success_rate(1.0)
        test_data = TestDataGenerator.generate_sensor_data()
        result = mock_client.publish_sensor_data(test_data)
        assert result is True
    
    def test_mock_mqtt_client_delay_simulation(self):
        """测试Mock MQTT客户端延迟模拟"""
        mock_client = MockMQTTClient("TEST_DEVICE_001")
        mock_client.connect()
        
        # 设置发布延迟
        mock_client.set_publish_delay(0.1)  # 100ms延迟
        
        test_data = TestDataGenerator.generate_sensor_data()
        
        start_time = time.time()
        result = mock_client.publish_sensor_data(test_data)
        end_time = time.time()
        
        assert result is True
        assert (end_time - start_time) >= 0.1  # 至少有100ms延迟
    
    def test_mock_mqtt_broker_functionality(self):
        """测试Mock MQTT代理功能"""
        broker = MockMQTTBroker()
        broker.start()
        
        # 创建两个客户端
        client1 = MockMQTTClient("CLIENT_001")
        client2 = MockMQTTClient("CLIENT_002")
        
        # 注册到代理
        broker.register_client("CLIENT_001", client1)
        broker.register_client("CLIENT_002", client2)
        
        # 发布消息
        broker.publish_message("test/topic", "test message", "CLIENT_001")
        
        # 检查消息历史
        messages = broker.get_messages()
        assert len(messages) == 1
        assert messages[0]["topic"] == "test/topic"
        assert messages[0]["payload"] == "test message"
        assert messages[0]["sender_id"] == "CLIENT_001"
        
        broker.stop()
