#!/bin/bash

# IoT传感器监控系统 - 性能压力测试脚本
# 测试系统在高负载下的表现

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置变量
BOARD_IP=${BOARD_IP:-"localhost"}
API_PORT=${API_PORT:-"5001"}
BASE_URL="http://${BOARD_IP}:${API_PORT}"
CONCURRENT_REQUESTS=${CONCURRENT_REQUESTS:-10}
TOTAL_REQUESTS=${TOTAL_REQUESTS:-100}
TEST_DURATION=${TEST_DURATION:-60}

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 性能测试函数
performance_test() {
    local test_name=$1
    local endpoint=$2
    local method=${3:-"GET"}
    local data=$4
    
    log_info "开始性能测试: $test_name"
    
    local start_time=$(date +%s)
    local success_count=0
    local error_count=0
    
    # 并发请求测试
    for ((i=1; i<=CONCURRENT_REQUESTS; i++)); do
        {
            for ((j=1; j<=TOTAL_REQUESTS/CONCURRENT_REQUESTS; j++)); do
                if [ "$method" = "GET" ]; then
                    if curl -s -f "$BASE_URL$endpoint" > /dev/null 2>&1; then
                        ((success_count++))
                    else
                        ((error_count++))
                    fi
                else
                    if [ -n "$data" ]; then
                        if curl -s -f -X "$method" \
                            -H "Content-Type: application/json" \
                            -d "$data" \
                            "$BASE_URL$endpoint" > /dev/null 2>&1; then
                            ((success_count++))
                        else
                            ((error_count++))
                        fi
                    else
                        if curl -s -f -X "$method" \
                            "$BASE_URL$endpoint" > /dev/null 2>&1; then
                            ((success_count++))
                        else
                            ((error_count++))
                        fi
                    fi
                fi
            done
        } &
    done
    
    # 等待所有后台任务完成
    wait
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    local total_requests=$((success_count + error_count))
    local success_rate=$(echo "scale=2; $success_count * 100 / $total_requests" | bc -l 2>/dev/null || echo "N/A")
    local rps=$(echo "scale=2; $total_requests / $duration" | bc -l 2>/dev/null || echo "N/A")
    
    echo "  总请求数: $total_requests"
    echo "  成功请求: $success_count"
    echo "  失败请求: $error_count"
    echo "  成功率: ${success_rate}%"
    echo "  测试时长: ${duration}秒"
    echo "  平均RPS: $rps"
    echo ""
}

# 压力测试函数
stress_test() {
    log_info "开始压力测试 (持续 ${TEST_DURATION} 秒)"
    
    # 启动性能监控
    monitor_data="{\"interval\": 1, \"duration\": $TEST_DURATION}"
    curl -s -X POST \
        -H "Content-Type: application/json" \
        -d "$monitor_data" \
        "$BASE_URL/api/monitor/start" > /dev/null
    
    local start_time=$(date +%s)
    local end_time=$((start_time + TEST_DURATION))
    local request_count=0
    
    # 持续发送请求
    while [ $(date +%s) -lt $end_time ]; do
        {
            # 混合不同类型的请求
            curl -s "$BASE_URL/api/system/status" > /dev/null 2>&1 &
            
            co2_data="{\"sensor_type\": \"co2\", \"value\": $((400 + RANDOM % 200))}"
            curl -s -X POST \
                -H "Content-Type: application/json" \
                -d "$co2_data" \
                "$BASE_URL/api/data/inject/sensor" > /dev/null 2>&1 &
            
            curl -s "$BASE_URL/api/mock/mqtt/messages?limit=5" > /dev/null 2>&1 &
            
            ((request_count += 3))
        }
        
        # 控制请求频率
        sleep 0.1
    done
    
    # 等待所有请求完成
    wait
    
    # 停止监控并获取结果
    sleep 2
    curl -s -X POST "$BASE_URL/api/monitor/stop" > /tmp/monitor_result.json
    
    log_success "压力测试完成"
    echo "  总请求数: $request_count"
    echo "  测试时长: ${TEST_DURATION}秒"
    echo "  平均RPS: $(echo "scale=2; $request_count / $TEST_DURATION" | bc -l 2>/dev/null || echo "N/A")"
    
    # 显示系统资源使用情况
    if [ -f /tmp/monitor_result.json ]; then
        log_info "系统资源使用情况:"
        if command -v jq &> /dev/null; then
            jq -r '.statistics // empty' /tmp/monitor_result.json 2>/dev/null || echo "  (无法解析监控数据)"
        else
            echo "  (需要安装jq来解析监控数据)"
        fi
    fi
    
    echo ""
}

# 内存泄漏测试
memory_leak_test() {
    log_info "开始内存泄漏测试"
    
    # 启动长时间监控
    monitor_data='{"interval": 5, "duration": 300}'
    curl -s -X POST \
        -H "Content-Type: application/json" \
        -d "$monitor_data" \
        "$BASE_URL/api/monitor/start" > /dev/null
    
    # 持续5分钟的数据注入
    for ((i=1; i<=300; i++)); do
        co2_data="{\"sensor_type\": \"co2\", \"value\": $((400 + i % 200))}"
        curl -s -X POST \
            -H "Content-Type: application/json" \
            -d "$co2_data" \
            "$BASE_URL/api/data/inject/sensor" > /dev/null 2>&1
        
        temp_data="{\"sensor_type\": \"temperature\", \"value\": $((20 + i % 20))}"
        curl -s -X POST \
            -H "Content-Type: application/json" \
            -d "$temp_data" \
            "$BASE_URL/api/data/inject/sensor" > /dev/null 2>&1
        
        sleep 1
    done
    
    # 获取监控结果
    curl -s -X POST "$BASE_URL/api/monitor/stop" > /tmp/memory_test.json
    
    log_success "内存泄漏测试完成"
    echo "  测试时长: 5分钟"
    echo "  数据注入次数: 600次"
    
    if [ -f /tmp/memory_test.json ] && command -v jq &> /dev/null; then
        echo "  内存使用趋势: (查看详细监控数据)"
    fi
    
    echo ""
}

# 主测试流程
main() {
    echo "========================================"
    echo "IoT传感器监控系统 - 性能压力测试"
    echo "测试目标: $BASE_URL"
    echo "并发数: $CONCURRENT_REQUESTS"
    echo "总请求数: $TOTAL_REQUESTS"
    echo "压力测试时长: ${TEST_DURATION}秒"
    echo "========================================"
    
    # 检查服务状态
    log_info "检查服务状态..."
    if ! curl -s "$BASE_URL/api/health" > /dev/null; then
        log_error "无法连接到测试服务"
        exit 1
    fi
    log_success "服务连接正常"
    
    # 1. 基础性能测试
    log_info "=== 基础性能测试 ==="
    
    performance_test "健康检查接口" "/api/health" "GET"
    performance_test "系统状态接口" "/api/system/status" "GET"
    performance_test "系统信息接口" "/api/info" "GET"
    
    # 2. 数据注入性能测试
    log_info "=== 数据注入性能测试 ==="
    
    co2_data='{"sensor_type": "co2", "value": 450, "unit": "ppm"}'
    performance_test "CO2数据注入" "/api/data/inject/sensor" "POST" "$co2_data"
    
    temp_data='{"sensor_type": "temperature", "value": 25.5, "unit": "°C"}'
    performance_test "温度数据注入" "/api/data/inject/sensor" "POST" "$temp_data"
    
    batch_data='{"batch_id": "perf_test", "data": [{"sensor_type": "co2", "value": 420}, {"sensor_type": "temperature", "value": 24.8}]}'
    performance_test "批量数据注入" "/api/data/inject/batch" "POST" "$batch_data"
    
    # 3. 查询性能测试
    log_info "=== 查询性能测试 ==="
    
    performance_test "MQTT消息查询" "/api/mock/mqtt/messages?limit=10" "GET"
    performance_test "监控数据查询" "/api/monitor/data?limit=5" "GET"
    
    # 4. 压力测试
    log_info "=== 系统压力测试 ==="
    stress_test
    
    # 5. 内存泄漏测试 (可选，时间较长)
    if [ "${RUN_MEMORY_TEST:-false}" = "true" ]; then
        log_info "=== 内存泄漏测试 ==="
        memory_leak_test
    else
        log_warning "跳过内存泄漏测试 (设置 RUN_MEMORY_TEST=true 来启用)"
    fi
    
    # 测试完成
    log_success "🎉 性能测试完成！"
    echo "========================================"
    
    # 清理临时文件
    rm -f /tmp/monitor_result.json /tmp/memory_test.json
}

# 检查依赖
if ! command -v curl &> /dev/null; then
    log_error "curl 未安装，请先安装 curl"
    exit 1
fi

if ! command -v bc &> /dev/null; then
    log_warning "bc 未安装，无法计算统计数据"
fi

# 运行主测试
main "$@"
