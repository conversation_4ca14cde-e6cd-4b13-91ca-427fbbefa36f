# 网络配置指南

## 🌐 网络访问配置

### **配置概述**

测试API服务器配置为监听所有网络接口(`0.0.0.0`)，端口为`5001`，这意味着：
- ✅ 支持本地访问 (`localhost:5001`)
- ✅ 支持同网段访问 (`192.168.x.x:5001`)
- ✅ 支持跨网段访问 (需要路由配置)

## 📍 获取主板IP地址

### **Windows系统**
```bash
ipconfig
```

### **Linux系统**
```bash
# 方法1
ip addr show

# 方法2
ifconfig

# 方法3 - 仅显示IP
hostname -I
```

### **查找活动网络接口**
```bash
# Windows - 查看网络连接状态
netsh interface show interface

# Linux - 查看活动接口
ip link show up
```

## 🔧 访问方式配置

### **1. 本地访问 (同一台机器)**
```bash
# 基础健康检查
curl http://localhost:5001/api/health

# 获取系统信息
curl http://localhost:5001/api/info

# 查看API文档
curl http://localhost:5001/
```

### **2. 同网段访问 (局域网)**
```bash
# 假设主板IP为 *************
curl http://*************:5001/api/health

# 注入传感器数据
curl -X POST http://*************:5001/api/data/inject/sensor \
     -H "Content-Type: application/json" \
     -d '{"sensor_type": "co2", "value": 450}'

# 获取系统状态
curl http://*************:5001/api/system/status
```

### **3. 跨网段访问 (不同网络)**
```bash
# 假设主板公网IP为 *************
curl http://*************:5001/api/health

# 注意：需要配置端口转发和防火墙规则
```

## 🛡️ 防火墙配置

### **Windows防火墙**
```bash
# 添加入站规则允许5001端口
netsh advfirewall firewall add rule name="IoT Testing API" dir=in action=allow protocol=TCP localport=5001

# 查看现有规则
netsh advfirewall firewall show rule name="IoT Testing API"

# 删除规则（如需要）
netsh advfirewall firewall delete rule name="IoT Testing API"
```

### **Linux防火墙 (ufw)**
```bash
# 允许5001端口
sudo ufw allow 5001/tcp

# 查看状态
sudo ufw status

# 允许特定IP访问
sudo ufw allow from ***********/24 to any port 5001
```

### **Linux防火墙 (iptables)**
```bash
# 允许5001端口
sudo iptables -A INPUT -p tcp --dport 5001 -j ACCEPT

# 保存规则
sudo iptables-save > /etc/iptables/rules.v4
```

## 🔍 网络连通性测试

### **1. 端口连通性测试**
```bash
# 使用telnet测试端口
telnet 主板IP 5001

# 使用nc (netcat) 测试
nc -zv 主板IP 5001

# 使用nmap扫描端口
nmap -p 5001 主板IP
```

### **2. 网络延迟测试**
```bash
# ping测试基础连通性
ping 主板IP

# 使用curl测试HTTP响应时间
curl -w "@curl-format.txt" -o /dev/null -s http://主板IP:5001/api/health
```

### **3. 创建curl格式文件**
```bash
# 创建 curl-format.txt
cat > curl-format.txt << EOF
     time_namelookup:  %{time_namelookup}\n
        time_connect:  %{time_connect}\n
     time_appconnect:  %{time_appconnect}\n
    time_pretransfer:  %{time_pretransfer}\n
       time_redirect:  %{time_redirect}\n
  time_starttransfer:  %{time_starttransfer}\n
                     ----------\n
          time_total:  %{time_total}\n
EOF
```

## 🌍 常见网络场景

### **场景1: 测试人员在办公网络，主板在实验室网络**
```bash
# 需要网络管理员配置：
# 1. 路由规则
# 2. 防火墙开放
# 3. 可能需要VPN连接

# 测试命令
curl http://实验室主板IP:5001/api/health
```

### **场景2: 测试人员远程访问**
```bash
# 需要配置：
# 1. 端口转发 (路由器配置)
# 2. 动态DNS (如果IP变化)
# 3. 安全认证 (建议)

# 访问示例
curl http://公网IP:5001/api/health
```

### **场景3: 云服务器部署**
```bash
# 云服务器安全组配置：
# 1. 开放5001端口入站规则
# 2. 配置源IP限制
# 3. 启用HTTPS (生产环境)

# 访问示例
curl http://云服务器IP:5001/api/health
```

## 📱 测试工具推荐

### **1. 命令行工具**
- **curl**: 基础HTTP测试
- **httpie**: 更友好的HTTP客户端
- **wget**: 简单的HTTP请求

### **2. 图形界面工具**
- **Postman**: 功能强大的API测试工具
- **Insomnia**: 轻量级REST客户端
- **Thunder Client**: VS Code插件

### **3. 浏览器访问**
```
# 直接在浏览器中访问
http://主板IP:5001/

# 查看API文档和接口列表
http://主板IP:5001/api/info
```

## ⚠️ 安全注意事项

### **测试环境安全**
1. **仅在测试网络中开放**: 不要在生产网络中暴露测试接口
2. **IP白名单**: 限制允许访问的IP地址范围
3. **临时开放**: 测试完成后及时关闭端口
4. **监控访问**: 记录所有API访问日志

### **生产环境部署**
1. **启用认证**: 配置API密钥或其他认证机制
2. **使用HTTPS**: 加密传输数据
3. **限制功能**: 禁用危险的系统控制接口
4. **访问控制**: 实施严格的访问控制策略

## 🔧 故障排除

### **常见问题**

**1. 连接被拒绝**
```bash
# 检查服务是否启动
curl http://localhost:5001/api/health

# 检查端口监听
netstat -an | grep 5001  # Windows/Linux
ss -tlnp | grep 5001     # Linux
```

**2. 超时错误**
```bash
# 检查网络连通性
ping 主板IP

# 检查防火墙设置
# Windows: 检查Windows防火墙
# Linux: sudo ufw status
```

**3. 404错误**
```bash
# 检查URL路径
curl -v http://主板IP:5001/api/info

# 查看服务日志
tail -f logs/system.log
```

---

**版本**: 1.0.0  
**更新时间**: 2025-07-30  
**维护者**: IoT传感器监控系统开发团队
