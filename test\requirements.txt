# 测试依赖包

# 核心测试框架
pytest>=7.0.0
pytest-asyncio>=0.21.0
pytest-mock>=3.10.0
pytest-cov>=4.0.0
pytest-benchmark>=4.0.0
pytest-xdist>=3.0.0
pytest-timeout>=2.1.0

# HTTP API测试
requests>=2.28.0
flask>=2.2.0
flask-cors>=4.0.0

# MQTT测试
paho-mqtt>=1.6.0

# 性能和监控
psutil>=5.9.0
memory-profiler>=0.60.0

# 数据处理和Mock
responses>=0.22.0
freezegun>=1.2.0
factory-boy>=3.2.0

# 测试报告
pytest-html>=3.1.0
pytest-json-report>=1.5.0
allure-pytest>=2.12.0

# 代码质量
flake8>=5.0.0
black>=22.0.0
isort>=5.10.0

# 类型检查
mypy>=0.991
types-requests>=2.28.0
types-PyYAML>=6.0.0
