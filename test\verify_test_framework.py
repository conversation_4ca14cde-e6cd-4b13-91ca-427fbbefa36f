#!/usr/bin/env python3
"""
测试框架完整性验证脚本

检查测试框架的所有组件是否正确安装和配置。
"""

import sys
import importlib
from pathlib import Path


def check_file_exists(file_path, description):
    """检查文件是否存在"""
    if file_path.exists():
        print(f"✅ {description}: {file_path}")
        return True
    else:
        print(f"❌ {description}: {file_path} (文件不存在)")
        return False


def check_import(module_name, description):
    """检查模块是否可以导入"""
    try:
        importlib.import_module(module_name)
        print(f"✅ {description}: {module_name}")
        return True
    except ImportError as e:
        print(f"❌ {description}: {module_name} (导入失败: {e})")
        return False


def check_pytest_markers():
    """检查pytest标记配置"""
    pytest_ini = Path("pytest.ini")
    if not pytest_ini.exists():
        print("❌ pytest.ini 文件不存在")
        return False
    
    content = pytest_ini.read_text(encoding='utf-8')
    required_markers = ['unit', 'integration', 'performance', 'stress', 'api', 'slow', 'hardware', 'mqtt', 'sensor']
    
    missing_markers = []
    for marker in required_markers:
        if marker not in content:
            missing_markers.append(marker)
    
    if missing_markers:
        print(f"❌ pytest.ini 缺少标记: {', '.join(missing_markers)}")
        return False
    else:
        print("✅ pytest.ini 标记配置完整")
        return True


def main():
    """主函数"""
    print("IoT传感器监控系统 - 测试框架完整性验证")
    print("="*60)
    
    # 切换到test目录
    test_dir = Path(__file__).parent
    import os
    os.chdir(test_dir)
    
    checks_passed = 0
    total_checks = 0
    
    # 1. 检查核心配置文件
    print("\n1. 检查核心配置文件:")
    core_files = [
        ("conftest.py", "pytest配置和共享fixtures"),
        ("pytest.ini", "pytest配置文件"),
        ("requirements.txt", "测试依赖包"),
        ("README.md", "测试文档"),
        ("test_config.py", "测试配置管理")
    ]
    
    for file_name, desc in core_files:
        total_checks += 1
        if check_file_exists(Path(file_name), desc):
            checks_passed += 1
    
    # 2. 检查目录结构
    print("\n2. 检查目录结构:")
    directories = [
        ("fixtures", "测试夹具目录"),
        ("utils", "测试工具目录"),
        ("unit", "单元测试目录"),
        ("integration", "集成测试目录"),
        ("performance", "性能测试目录"),
        ("stress", "压力测试目录"),
        ("api", "API测试目录"),
        ("scripts", "测试脚本目录")
    ]
    
    for dir_name, desc in directories:
        total_checks += 1
        if check_file_exists(Path(dir_name), desc):
            checks_passed += 1
    
    # 3. 检查测试夹具文件
    print("\n3. 检查测试夹具文件:")
    fixture_files = [
        ("fixtures/mock_sensors.py", "Mock传感器实现"),
        ("fixtures/mock_mqtt.py", "Mock MQTT客户端"),
        ("fixtures/test_data.py", "测试数据生成器"),
        ("fixtures/config_samples.py", "测试配置样例")
    ]
    
    for file_name, desc in fixture_files:
        total_checks += 1
        if check_file_exists(Path(file_name), desc):
            checks_passed += 1
    
    # 4. 检查测试文件
    print("\n4. 检查测试文件:")
    test_files = [
        ("unit/test_sensors/test_sensor_collector.py", "传感器收集器单元测试"),
        ("unit/test_communication/test_mqtt_client.py", "MQTT客户端单元测试"),
        ("unit/test_utils/test_device_health_check.py", "设备健康检查单元测试"),
        ("integration/test_sensor_to_mqtt_flow.py", "传感器到MQTT数据流集成测试"),
        ("performance/test_sensor_performance.py", "传感器系统性能测试"),
        ("stress/test_system_stress.py", "系统压力测试"),
        ("api/test_http_api.py", "HTTP API测试"),
        ("api/test_mqtt_interface.py", "MQTT接口测试")
    ]
    
    for file_name, desc in test_files:
        total_checks += 1
        if check_file_exists(Path(file_name), desc):
            checks_passed += 1
    
    # 5. 检查工具文件
    print("\n5. 检查工具文件:")
    util_files = [
        ("utils/test_helpers.py", "测试辅助函数"),
        ("scripts/run_tests.py", "测试执行脚本"),
        ("run_example_tests.py", "测试运行示例脚本")
    ]
    
    for file_name, desc in util_files:
        total_checks += 1
        if check_file_exists(Path(file_name), desc):
            checks_passed += 1
    
    # 6. 检查Python模块导入
    print("\n6. 检查Python模块导入:")
    modules = [
        ("pytest", "pytest测试框架"),
        ("unittest.mock", "Mock对象库"),
        ("flask", "Flask Web框架"),
        ("psutil", "系统监控库"),
        ("threading", "多线程库"),
        ("json", "JSON处理库"),
        ("time", "时间处理库")
    ]
    
    for module_name, desc in modules:
        total_checks += 1
        if check_import(module_name, desc):
            checks_passed += 1
    
    # 7. 检查pytest配置
    print("\n7. 检查pytest配置:")
    total_checks += 1
    if check_pytest_markers():
        checks_passed += 1
    
    # 8. 检查测试配置
    print("\n8. 检查测试配置:")
    try:
        from test_config import TestConfig, TestPaths, get_test_config
        config = get_test_config('base')
        paths = TestPaths()
        
        print("✅ 测试配置模块导入成功")
        print(f"✅ 基础配置加载成功: {len(config)} 个配置项")
        print(f"✅ 测试路径管理正常: {paths.test_root}")
        
        total_checks += 3
        checks_passed += 3
        
    except Exception as e:
        print(f"❌ 测试配置检查失败: {e}")
        total_checks += 3
    
    # 总结
    print(f"\n{'='*60}")
    print("验证结果总结")
    print('='*60)
    print(f"总检查项: {total_checks}")
    print(f"通过: {checks_passed}")
    print(f"失败: {total_checks - checks_passed}")
    print(f"通过率: {checks_passed/total_checks*100:.1f}%")
    
    if checks_passed == total_checks:
        print("\n🎉 测试框架完整性验证通过！所有组件都已正确配置。")
        print("\n下一步:")
        print("1. 运行 'python run_example_tests.py' 验证测试功能")
        print("2. 运行 'python -m pytest test/ -v' 执行所有测试")
        print("3. 运行 'python scripts/run_tests.py --help' 查看测试脚本选项")
        return 0
    else:
        print(f"\n⚠️  测试框架不完整，有 {total_checks - checks_passed} 个检查项失败。")
        print("请检查上面的错误信息并修复相关问题。")
        return 1


if __name__ == "__main__":
    sys.exit(main())
