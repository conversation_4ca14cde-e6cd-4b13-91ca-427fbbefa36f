"""
HTTP API测试服务器

提供用于测试的HTTP API接口，支持数据注入、监控和故障模拟。
"""

import json
import time
import threading
import pytest
from flask import Flask, request, jsonify
from flask_cors import CORS
from typing import Dict, Any, Optional


class TestAPIServer:
    """测试API服务器"""
    
    def __init__(self, host='0.0.0.0', port=5000):
        self.host = host
        self.port = port
        self.app = Flask(__name__)
        CORS(self.app)
        
        # 测试数据存储
        self.injected_data = []
        self.system_status = {}
        self.sensor_system = None
        
        # 设置路由
        self._setup_routes()
        
    def _setup_routes(self):
        """设置API路由"""
        
        @self.app.route('/api/health', methods=['GET'])
        def health_check():
            """健康检查"""
            return jsonify({
                "status": "ok",
                "message": "测试API服务器运行正常",
                "timestamp": time.time()
            })
        
        @self.app.route('/api/inject/sensor', methods=['POST'])
        def inject_sensor_data():
            """注入传感器数据"""
            try:
                data = request.get_json()
                if not data:
                    return jsonify({"error": "无效的JSON数据"}), 400
                
                # 添加时间戳
                data['injected_at'] = time.time()
                self.injected_data.append(data)
                
                # 如果有传感器系统实例，尝试注入数据
                if self.sensor_system and hasattr(self.sensor_system, 'mqtt_client'):
                    mqtt_client = self.sensor_system.mqtt_client
                    if mqtt_client:
                        success = mqtt_client.publish_sensor_data(data)
                        return jsonify({
                            "status": "success" if success else "failed",
                            "message": "数据已注入" if success else "数据注入失败",
                            "data": data
                        })
                
                return jsonify({
                    "status": "success",
                    "message": "数据已接收（离线模式）",
                    "data": data
                })
                
            except Exception as e:
                return jsonify({"error": str(e)}), 500
        
        @self.app.route('/api/monitor/status', methods=['GET'])
        def get_system_status():
            """获取系统状态"""
            status = {
                "timestamp": time.time(),
                "injected_data_count": len(self.injected_data),
                "system_connected": self.sensor_system is not None
            }
            
            if self.sensor_system:
                status.update({
                    "sensor_collector_running": getattr(self.sensor_system.sensor_collector, 'running', False) if self.sensor_system.sensor_collector else False,
                    "mqtt_connected": getattr(self.sensor_system.mqtt_client, 'connected', False) if self.sensor_system.mqtt_client else False
                })
            
            return jsonify(status)
        
        @self.app.route('/api/data/injected', methods=['GET'])
        def get_injected_data():
            """获取已注入的数据"""
            limit = request.args.get('limit', 100, type=int)
            return jsonify({
                "data": self.injected_data[-limit:],
                "total": len(self.injected_data)
            })
        
        @self.app.route('/api/data/clear', methods=['POST'])
        def clear_injected_data():
            """清空已注入的数据"""
            count = len(self.injected_data)
            self.injected_data.clear()
            return jsonify({
                "status": "success",
                "message": f"已清空 {count} 条数据"
            })
    
    def set_sensor_system(self, sensor_system):
        """设置传感器系统实例"""
        self.sensor_system = sensor_system
    
    def run(self, debug=False):
        """运行服务器"""
        self.app.run(host=self.host, port=self.port, debug=debug, threaded=True)


def run_test_server(host='0.0.0.0', port=5000, system=None):
    """运行测试服务器的便捷函数"""
    server = TestAPIServer(host, port)
    if system:
        server.set_sensor_system(system)
    server.run()


@pytest.mark.api
class TestHTTPAPI:
    """HTTP API测试类"""

    @pytest.fixture
    def client(self):
        """创建测试客户端"""
        server = TestAPIServer('localhost', 5000)
        server.app.config['TESTING'] = True
        with server.app.test_client() as client:
            yield client

    def test_health_endpoint(self, client):
        """测试健康检查端点"""
        response = client.get('/api/health')
        assert response.status_code == 200

        data = response.get_json()
        assert 'status' in data
        assert 'timestamp' in data
        assert data['status'] == 'healthy'

    def test_inject_sensor_data(self, client):
        """测试注入传感器数据"""
        test_data = {
            'temperature': 25.5,
            'humidity': 60.0,
            'co2': 400,
            'timestamp': int(time.time())
        }

        response = client.post('/api/inject/sensor_data',
                             data=json.dumps(test_data),
                             content_type='application/json')

        assert response.status_code == 200
        data = response.get_json()
        assert data['success'] is True

    def test_inject_invalid_sensor_data(self, client):
        """测试注入无效传感器数据"""
        invalid_data = {
            'temperature': 'invalid',  # 无效类型
            'humidity': -10,  # 无效范围
        }

        response = client.post('/api/inject/sensor_data',
                             data=json.dumps(invalid_data),
                             content_type='application/json')

        assert response.status_code == 400
        data = response.get_json()
        assert data['success'] is False
        assert 'error' in data

    def test_system_info_endpoint(self, client):
        """测试系统信息端点"""
        response = client.get('/api/system/info')
        assert response.status_code == 200

        data = response.get_json()
        assert 'cpu_usage' in data
        assert 'memory_usage' in data
        assert 'disk_usage' in data
        assert 'uptime' in data

    def test_device_health_endpoint(self, client):
        """测试设备健康端点"""
        response = client.get('/api/device/health')
        assert response.status_code == 200

        data = response.get_json()
        assert 'device_id' in data
        assert 'components' in data
        assert 'overall_status' in data


if __name__ == "__main__":
    # 独立运行测试服务器
    print("启动测试API服务器...")
    print("访问 http://localhost:5000/api/health 检查服务器状态")
    run_test_server()
