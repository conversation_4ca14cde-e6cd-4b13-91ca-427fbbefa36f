# 测试环境配置文件
# 基于 config.yaml，专门用于测试模式，确保完全环境隔离

# 测试模式配置
test_mode:
  enabled: true                     # 启用测试模式
  mock_mqtt: true                   # 模拟MQTT通信
  mock_hardware: true               # 模拟硬件设备
  data_isolation: true              # 数据完全隔离
  simulate_responses: true          # 模拟服务器响应
  test_session_id: ""               # 测试会话ID，运行时生成
  
  # 模拟响应配置
  mock_responses:
    mqtt_publish_success_rate: 0.95  # MQTT发布成功率
    mqtt_response_delay: 0.1         # 模拟响应延迟(秒)
    server_error_rate: 0.02          # 服务器错误率
    timeout_rate: 0.01               # 超时率
    network_failure_rate: 0.005      # 网络故障率

# 设备绑定配置（测试模式）
device_binding:
  device_id: "TEST_DEVICE_001"      # 测试设备ID
  device_name: "IoT测试设备"
  location: "测试环境"
  version: "1.0.0"

# MQTT配置（测试模式 - 完全模拟）
mqtt:
  enable_mqtt_connection: false     # 禁用真实MQTT连接
  mock_mode: true                   # 启用模拟模式
  
  # 保留原有配置结构用于模拟
  broker: "mock.mqtt.broker"
  port: 1883
  username: "test_user"
  password: "test_password"
  
  # 主题配置（用于模拟）
  sensor_topic: "/xxc/attr/sensor/{device_id}"
  co2_topic: "/xxc/attr/co2/{device_id}"
  check_topic: "/xxc/attr/check/{device_id}"
  mosquito_topic: "/xxc/attr/mosqutio/{device_id}"
  
  # 回复主题配置
  sensor_reply_topic: "/xxc/once/sensor/{device_id}/reply"
  co2_reply_topic: "/xxc/once/co2/{device_id}/reply"
  check_reply_topic: "/xxc/once/check/{device_id}/reply"
  mosquito_reply_topic: "/xxc/once/mosqutio/{device_id}/reply"
  
  # 蚊子检测配置（模拟模式）
  mosquito_detection:
    enabled: true
    shared_data_path: "test_data/mosquito_shared"
    file_check_interval: 5
    max_retry_count: 3
    
  # 时间间隔配置
  publish_interval: 60              # 数据上传间隔（秒）
  message_upload_interval: 0.1      # 消息上传间隔（秒），测试模式加快
  batch_size: 5
  check_batch_size: 20
  error_check_interval: 60
  confirmation_timeout: 5
  
  # 连接管理配置
  reconnect_base_interval: 5
  reconnect_max_interval: 60
  reconnect_max_attempts: 0
  network_check_interval: 10
  
  # 内存队列配置
  sensor_queue_size: 100
  error_queue_size: 50
  co2_queue_size: 20
  check_queue_size: 50
  mosquito_queue_size: 50
  
  # 消息去重配置
  message_expiry: 3600
  max_message_cache: 1000

# 串口配置（测试模式 - 模拟）
serial:
  port: "/dev/mock_ttyTHS1"         # 模拟串口
  baudrate: 4800
  timeout: 1
  mock_mode: true                   # 启用串口模拟

# 传感器配置（与生产环境完全一致的验证规则）
sensors:
  cth:  # 温湿度CO2传感器
    device_address: 0x01
    # 数据质量检验阈值（与config.yaml完全一致）
    temperature_min: 0              # 最低温度 (°C)
    temperature_max: 60             # 最高温度 (°C)
    humidity_min: 0                 # 最低湿度 (%)
    humidity_max: 95                # 最高湿度 (%)
    co2_min: 0                      # 最低CO2浓度 (ppm)
    co2_max: 50000                  # 最高CO2浓度 (ppm)
  gps:  # GPS模块
    device_address: 0x02
  wind_speed:  # 风速传感器
    device_address: 0x04
    min: 0                          # 最低风速 (m/s)
    max: 60                         # 最高风速 (m/s)

# 数据采集配置
data_collection:
  interval: 60                      # 数据采集间隔（秒）

# 数据存储配置（测试模式 - 完全隔离）
data_storage:
  data_dir: "test_data"             # 独立测试数据目录
  sensors_dir: "sensors"
  devices_dir: "devices"
  sensor_data_file: "sensor_data.json"
  sensor_errors_file: "sensor_errors.json"
  sensor_data_archive_file: "sensor_data_archive.json"
  sensor_errors_archive_file: "sensor_errors_archive.json"
  co2_errors_file: "co2_errors.json"
  co2_errors_archive_file: "co2_errors_archive.json"
  credentials_file: "credentials.json"
  check_dir: "check"
  
  # 数据限制
  max_pending: 1000
  max_history: 10000

# 重试机制配置
retry:
  base_interval: 5
  max_interval: 300
  temp_failure_count: 3
  perm_failure_count: 10

# CO2控制器配置（测试模式 - 模拟GPIO）
co2_controller:
  # 硬件配置（模拟）
  pins:
    fans: [13, 15]
    pumps: [16, 21]
    heater: 19
    stepper: [22, 24, 26, 29]
  mock_mode: true                   # 启用GPIO模拟
  
  # 时间配置（分钟）
  timing:
    fan_switch_interval: 20
    adsorption_duration:
      round1: 110
      round2: 110
      round3: 110
      round4: 190
    heating_duration: 25
    pump_switch_interval: 2
    release_duration: 55
  
  # 异常处理配置
  retry_count: 3

# 设备自检配置（测试模式）
device_health:
  check_interval: 300               # 自检间隔（秒），测试模式缩短
  sensor_data_expiry: 600

  # 退避策略配置
  backoff:
    initial_interval: 60
    max_interval: 1800
    multiplier: 2

  # 状态检测配置
  state_detection:
    same_state_window: 300
    recovery_check_interval: 30

  # 上报控制
  reporting:
    immediate_scenarios:
      - "first_permanent_failure"
      - "recovery_to_normal"
      - "scheduled_check"
    backoff_scenarios:
      - "repeated_failure"
      - "permanent_failure_retry"

  # 资源使用阈值
  cpu_threshold: 90
  memory_threshold: 10
  disk_threshold: 10
  gpu_threshold: 90
  battery_threshold: 20
  battery_min_voltage: 10.5
  battery_max_voltage: 12.0
  i2c_bus: 7
  ads_address: 0x48
  battery_retry_count: 3
  battery_retry_delay: 0.1

# 缓存配置
cache:
  max_sensor_cache_size: 1000
  max_error_cache_size: 500
  cache_max_size: 50
  flush_interval: 5

  # 内存监控配置
  memory_monitor:
    check_interval: 30
    warning_threshold: 80
    critical_threshold: 90
    max_memory_mb: 1024

# 异步文件I/O配置
async_file_io:
  max_queue_size: 1000
  worker_threads: 2
  batch_size: 20
  flush_interval: 3
  enable_compression: false

# 线程管理配置
thread_manager:
  max_workers: 8
  core_workers: 4
  queue_size: 1000
  health_check_interval: 30
  task_timeout: 7200
  enable_monitoring: true

# 测试API配置
test_api:
  enabled: true                     # 启用测试API
  host: "0.0.0.0"                   # API服务器地址
  port: 5001                        # API服务器端口
  debug: true                       # 调试模式

  # API安全配置
  security:
    enable_auth: false              # 测试环境暂时禁用认证
    api_key: "test_api_key_12345"
    rate_limit: 1000                # 每分钟请求限制

  # 测试数据配置
  test_data:
    max_injection_rate: 100         # 最大数据注入速率（条/秒）
    max_batch_size: 1000            # 最大批量数据大小
    retention_days: 7               # 测试数据保留天数

  # 报告配置
  reports:
    output_dir: "test_data/reports"
    formats: ["html", "json", "pdf"]
    auto_cleanup: true
    max_reports: 100

# 日志配置（测试模式）
logging:
  level: "DEBUG"                    # 测试模式使用DEBUG级别
  file: "test_data/logs/test.log"
  max_size: "10MB"
  backup_count: 5
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
