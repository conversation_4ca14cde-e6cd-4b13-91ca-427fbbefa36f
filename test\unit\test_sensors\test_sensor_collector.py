"""
传感器收集器单元测试

测试传感器数据收集器的各项功能。
"""

import pytest
import time
from unittest.mock import Mock, patch, MagicMock
from test.fixtures.mock_sensors import MockCTHSensor, MockGPSSensor, MockWindSpeedSensor
from test.fixtures.test_data import TestDataGenerator
from test.fixtures.config_samples import get_test_config


@pytest.mark.unit
class TestSensorCollector:
    """传感器收集器测试类"""
    
    def setup_method(self):
        """测试前设置"""
        self.config = get_test_config("base")
        self.mock_logger = Mock()
        
    def test_sensor_collector_import(self):
        """测试传感器收集器模块导入"""
        try:
            from sensors.sensor_collector import SensorCollector
            assert SensorCollector is not None
        except ImportError as e:
            pytest.skip(f"传感器收集器模块未找到: {e}")
    
    @patch('sensors.sensor_collector.serial.Serial')
    @patch('sensors.sensor_collector.get_logger')
    def test_sensor_collector_initialization(self, mock_get_logger, mock_serial):
        """测试传感器收集器初始化"""
        try:
            from sensors.sensor_collector import SensorCollector
            
            mock_get_logger.return_value = self.mock_logger
            mock_serial.return_value = Mock()
            
            collector = SensorCollector(self.config)
            
            assert collector is not None
            assert collector.config == self.config
            assert hasattr(collector, 'running')
            
        except ImportError:
            pytest.skip("传感器收集器模块未找到")
    
    @patch('sensors.sensor_collector.serial.Serial')
    @patch('sensors.sensor_collector.get_logger')
    def test_collect_cth_data_success(self, mock_get_logger, mock_serial):
        """测试成功收集CTH数据"""
        try:
            from sensors.sensor_collector import SensorCollector
            
            mock_get_logger.return_value = self.mock_logger
            
            # 模拟串口响应
            mock_serial_instance = Mock()
            mock_serial_instance.is_open = True
            mock_serial_instance.write.return_value = 8
            # CTH传感器响应数据 (温度25.5°C, 湿度60.2%, CO2 400ppm)
            mock_serial_instance.read.return_value = b'\x01\x03\x06\x00\xFF\x02\x5A\x01\x90\x9C\x8F'
            mock_serial.return_value = mock_serial_instance
            
            collector = SensorCollector(self.config)
            data = collector.get_cth_data()
            
            assert data is not None
            assert 'temperature' in data
            assert 'humidity' in data
            assert 'co2' in data
            assert isinstance(data['temperature'], (int, float))
            assert isinstance(data['humidity'], (int, float))
            assert isinstance(data['co2'], int)
            
        except ImportError:
            pytest.skip("传感器收集器模块未找到")
    
    @patch('sensors.sensor_collector.serial.Serial')
    @patch('sensors.sensor_collector.get_logger')
    def test_collect_gps_data_success(self, mock_get_logger, mock_serial):
        """测试成功收集GPS数据"""
        try:
            from sensors.sensor_collector import SensorCollector
            
            mock_get_logger.return_value = self.mock_logger
            
            # 模拟串口响应
            mock_serial_instance = Mock()
            mock_serial_instance.is_open = True
            mock_serial_instance.write.return_value = 8
            # GPS传感器响应数据
            mock_serial_instance.read.return_value = b'\x02\x03\x08\x42\x1F\xE4\x7A\x42\xE9\x1E\xB8\x9C\x8F'
            mock_serial.return_value = mock_serial_instance
            
            collector = SensorCollector(self.config)
            data = collector.get_gps_data()
            
            assert data is not None
            assert 'latitude' in data
            assert 'longitude' in data
            assert isinstance(data['latitude'], float)
            assert isinstance(data['longitude'], float)
            
        except ImportError:
            pytest.skip("传感器收集器模块未找到")
    
    @patch('sensors.sensor_collector.serial.Serial')
    @patch('sensors.sensor_collector.get_logger')
    def test_collect_wind_speed_success(self, mock_get_logger, mock_serial):
        """测试成功收集风速数据"""
        try:
            from sensors.sensor_collector import SensorCollector
            
            mock_get_logger.return_value = self.mock_logger
            
            # 模拟串口响应
            mock_serial_instance = Mock()
            mock_serial_instance.is_open = True
            mock_serial_instance.write.return_value = 8
            # 风速传感器响应数据 (风速3.2m/s)
            mock_serial_instance.read.return_value = b'\x04\x03\x02\x00\x20\x9C\x8F'
            mock_serial.return_value = mock_serial_instance
            
            collector = SensorCollector(self.config)
            data = collector.get_wind_speed()
            
            assert data is not None
            assert 'wind_speed' in data
            assert isinstance(data['wind_speed'], (int, float))
            
        except ImportError:
            pytest.skip("传感器收集器模块未找到")
    
    @patch('sensors.sensor_collector.serial.Serial')
    @patch('sensors.sensor_collector.get_logger')
    def test_serial_communication_error(self, mock_get_logger, mock_serial):
        """测试串口通信错误处理"""
        try:
            from sensors.sensor_collector import SensorCollector
            
            mock_get_logger.return_value = self.mock_logger
            
            # 模拟串口错误
            mock_serial_instance = Mock()
            mock_serial_instance.is_open = False
            mock_serial.return_value = mock_serial_instance
            
            collector = SensorCollector(self.config)
            data = collector.get_cth_data()
            
            # 应该返回None或抛出异常
            assert data is None or isinstance(data, dict)
            
            # 检查是否记录了错误日志
            assert self.mock_logger.error.called or self.mock_logger.warning.called
            
        except ImportError:
            pytest.skip("传感器收集器模块未找到")
    
    @patch('sensors.sensor_collector.serial.Serial')
    @patch('sensors.sensor_collector.get_logger')
    def test_collect_all_data(self, mock_get_logger, mock_serial):
        """测试收集所有传感器数据"""
        try:
            from sensors.sensor_collector import SensorCollector
            
            mock_get_logger.return_value = self.mock_logger
            
            # 模拟串口
            mock_serial_instance = Mock()
            mock_serial_instance.is_open = True
            mock_serial_instance.write.return_value = 8
            mock_serial_instance.read.return_value = b'\x01\x03\x06\x00\xFF\x02\x5A\x01\x90\x9C\x8F'
            mock_serial.return_value = mock_serial_instance
            
            collector = SensorCollector(self.config)
            
            # 模拟各个传感器方法
            collector.get_cth_data = Mock(return_value={
                'temperature': 25.5,
                'humidity': 60.2,
                'co2': 400
            })
            collector.get_gps_data = Mock(return_value={
                'latitude': 39.9042,
                'longitude': 116.4074
            })
            collector.get_wind_speed = Mock(return_value={
                'wind_speed': 3.2
            })
            
            data = collector.collect_data()
            
            assert data is not None
            assert 'timestamp' in data
            assert 'datetime' in data
            assert 'temperature' in data
            assert 'humidity' in data
            assert 'co2' in data
            assert 'latitude' in data
            assert 'longitude' in data
            assert 'wind_speed' in data
            
        except ImportError:
            pytest.skip("传感器收集器模块未找到")
    
    def test_mock_sensors_functionality(self):
        """测试Mock传感器功能"""
        # 测试CTH传感器Mock
        cth_sensor = MockCTHSensor()
        data = cth_sensor.read_data()
        assert data is not None
        assert 'temperature' in data
        assert 'humidity' in data
        assert 'co2' in data
        
        # 测试GPS传感器Mock
        gps_sensor = MockGPSSensor()
        data = gps_sensor.read_data()
        assert data is not None
        assert 'latitude' in data
        assert 'longitude' in data
        
        # 测试风速传感器Mock
        wind_sensor = MockWindSpeedSensor()
        data = wind_sensor.read_data()
        assert data is not None
        assert 'wind_speed' in data
    
    def test_mock_sensors_error_simulation(self):
        """测试Mock传感器错误模拟"""
        cth_sensor = MockCTHSensor()
        
        # 设置100%错误率
        cth_sensor.set_error_rate(1.0)
        data = cth_sensor.read_data()
        assert data is None
        
        # 恢复正常
        cth_sensor.set_error_rate(0.0)
        data = cth_sensor.read_data()
        assert data is not None
        
        # 断开连接测试
        cth_sensor.disconnect()
        data = cth_sensor.read_data()
        assert data is None
        
        # 重新连接
        cth_sensor.reconnect()
        data = cth_sensor.read_data()
        assert data is not None
