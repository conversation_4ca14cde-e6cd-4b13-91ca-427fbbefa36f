# 测试模块初始化文件
"""
传感器系统测试套件

这个测试套件提供了完整的测试覆盖，包括：
- 单元测试：测试各个模块的功能
- 集成测试：测试模块间的协作
- 性能测试：评估系统性能指标
- 压力测试：验证系统稳定性
- API测试：验证HTTP和MQTT接口

使用方法：
    # 运行所有测试
    python -m pytest test/ -v
    
    # 运行特定类型测试
    python -m pytest test/unit/ -v
    python -m pytest test/integration/ -v
    python -m pytest test/performance/ -v
    python -m pytest test/stress/ -v
    python -m pytest test/api/ -v
"""

__version__ = "1.0.0"
__author__ = "传感器系统开发团队"
