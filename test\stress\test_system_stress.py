"""
系统压力测试

测试系统在高负载、长时间运行等极端条件下的稳定性。
"""

import pytest
import time
import threading
import queue
from unittest.mock import Mock
from test.fixtures.mock_sensors import create_mock_sensor_collector
from test.fixtures.mock_mqtt import MockMQTTClient
from test.fixtures.test_data import TestDataGenerator
from test.fixtures.config_samples import get_test_config


@pytest.mark.stress
@pytest.mark.slow
class TestSystemStress:
    """系统压力测试类"""
    
    def setup_method(self):
        """测试前设置"""
        self.config = get_test_config("stress")
        self.mock_logger = Mock()
        
    def test_high_frequency_data_collection(self):
        """测试高频数据收集压力"""
        # 创建Mock组件
        sensor_collector = create_mock_sensor_collector(self.config)
        mqtt_client = MockMQTTClient(self.config['mqtt']['device_id'])
        mqtt_client.connect()
        
        # 高频数据收集（每10ms一次，持续10秒）
        test_duration = 10  # 10秒
        interval = 0.01  # 10ms
        
        start_time = time.time()
        success_count = 0
        error_count = 0
        
        while time.time() - start_time < test_duration:
            try:
                test_data = TestDataGenerator.generate_sensor_data()
                sensor_collector.collect_data.return_value = test_data
                
                collected_data = sensor_collector.collect_data()
                if collected_data:
                    result = mqtt_client.publish_sensor_data(collected_data)
                    if result:
                        success_count += 1
                    else:
                        error_count += 1
                else:
                    error_count += 1
                
                time.sleep(interval)
                
            except Exception as e:
                error_count += 1
                print(f"高频收集错误: {e}")
        
        # 验证压力测试结果
        total_attempts = success_count + error_count
        success_rate = success_count / total_attempts if total_attempts > 0 else 0
        
        print(f"高频数据收集结果: 成功 {success_count}, 失败 {error_count}, 成功率 {success_rate:.2%}")
        
        # 成功率应该大于95%
        assert success_rate > 0.95, f"高频收集成功率过低: {success_rate:.2%}"
        assert total_attempts > 900, f"处理数量不足: {total_attempts}"  # 至少处理900次
    
    def test_massive_concurrent_connections(self):
        """测试大量并发连接压力"""
        num_devices = 50  # 50个并发设备
        messages_per_device = 20  # 每个设备20条消息
        
        devices = []
        results = queue.Queue()
        
        def device_worker(device_id):
            """设备工作线程"""
            try:
                collector = create_mock_sensor_collector(self.config)
                mqtt_client = MockMQTTClient(f"STRESS_DEVICE_{device_id:03d}")
                mqtt_client.connect()
                
                success_count = 0
                for _ in range(messages_per_device):
                    test_data = TestDataGenerator.generate_sensor_data()
                    collector.collect_data.return_value = test_data
                    
                    collected_data = collector.collect_data()
                    if collected_data:
                        result = mqtt_client.publish_sensor_data(collected_data)
                        if result:
                            success_count += 1
                    
                    time.sleep(0.05)  # 50ms间隔
                
                results.put(('success', device_id, success_count))
                
            except Exception as e:
                results.put(('error', device_id, str(e)))
        
        # 启动所有设备线程
        threads = []
        for i in range(num_devices):
            thread = threading.Thread(target=device_worker, args=(i,))
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join(timeout=60)  # 60秒超时
        
        # 收集结果
        total_success = 0
        total_errors = 0
        
        while not results.empty():
            result_type, device_id, data = results.get()
            if result_type == 'success':
                total_success += data
            else:
                total_errors += 1
                print(f"设备 {device_id} 错误: {data}")
        
        # 验证并发压力测试结果
        expected_total = num_devices * messages_per_device
        success_rate = total_success / expected_total
        
        print(f"并发连接结果: 成功 {total_success}/{expected_total}, 错误设备 {total_errors}, 成功率 {success_rate:.2%}")
        
        # 成功率应该大于90%
        assert success_rate > 0.90, f"并发连接成功率过低: {success_rate:.2%}"
        assert total_errors < num_devices * 0.1, f"错误设备过多: {total_errors}"
    
    def test_memory_leak_detection(self):
        """测试内存泄漏检测"""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # 创建Mock组件
        sensor_collector = create_mock_sensor_collector(self.config)
        mqtt_client = MockMQTTClient(self.config['mqtt']['device_id'])
        mqtt_client.connect()
        
        memory_samples = []
        
        # 长时间运行测试（5分钟，每10秒检查一次内存）
        test_duration = 300  # 5分钟
        check_interval = 10  # 10秒
        
        start_time = time.time()
        last_check = start_time
        iteration_count = 0
        
        while time.time() - start_time < test_duration:
            # 执行数据处理
            for _ in range(100):  # 每次处理100条数据
                test_data = TestDataGenerator.generate_sensor_data()
                sensor_collector.collect_data.return_value = test_data
                
                collected_data = sensor_collector.collect_data()
                if collected_data:
                    mqtt_client.publish_sensor_data(collected_data)
                
                iteration_count += 1
            
            # 定期检查内存使用
            current_time = time.time()
            if current_time - last_check >= check_interval:
                current_memory = process.memory_info().rss / 1024 / 1024  # MB
                memory_samples.append(current_memory)
                last_check = current_time
                
                print(f"内存使用: {current_memory:.2f}MB (处理了 {iteration_count} 次)")
        
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory
        
        # 分析内存趋势
        if len(memory_samples) >= 3:
            # 检查内存是否持续增长
            memory_trend = memory_samples[-1] - memory_samples[0]
            avg_increase_per_check = memory_trend / len(memory_samples)
            
            print(f"内存泄漏检测: 初始 {initial_memory:.2f}MB, 最终 {final_memory:.2f}MB")
            print(f"总增长 {memory_increase:.2f}MB, 趋势 {memory_trend:.2f}MB, 平均增长 {avg_increase_per_check:.2f}MB/检查")
            
            # 验证内存泄漏（总增长不超过100MB，平均增长不超过5MB/检查）
            assert memory_increase < 100, f"内存增长过多: {memory_increase:.2f}MB"
            assert avg_increase_per_check < 5, f"内存持续增长: {avg_increase_per_check:.2f}MB/检查"
    
    def test_network_instability_resilience(self):
        """测试网络不稳定环境下的弹性"""
        # 创建Mock组件
        sensor_collector = create_mock_sensor_collector(self.config)
        mqtt_client = MockMQTTClient(self.config['mqtt']['device_id'])
        mqtt_client.connect()
        
        # 模拟网络不稳定（成功率在20%-80%之间波动）
        test_duration = 60  # 1分钟
        start_time = time.time()
        
        success_count = 0
        failure_count = 0
        network_changes = 0
        
        while time.time() - start_time < test_duration:
            # 每10秒改变网络条件
            elapsed = time.time() - start_time
            if int(elapsed) % 10 == 0 and int(elapsed) > network_changes * 10:
                # 随机设置成功率
                import random
                success_rate = random.uniform(0.2, 0.8)
                mqtt_client.set_publish_success_rate(success_rate)
                network_changes += 1
                print(f"网络条件变化 {network_changes}: 成功率 {success_rate:.2%}")
            
            # 执行数据处理
            test_data = TestDataGenerator.generate_sensor_data()
            sensor_collector.collect_data.return_value = test_data
            
            collected_data = sensor_collector.collect_data()
            if collected_data:
                result = mqtt_client.publish_sensor_data(collected_data)
                if result:
                    success_count += 1
                else:
                    failure_count += 1
            
            time.sleep(0.1)  # 100ms间隔
        
        # 验证网络弹性
        total_attempts = success_count + failure_count
        overall_success_rate = success_count / total_attempts if total_attempts > 0 else 0
        
        print(f"网络弹性测试: 成功 {success_count}, 失败 {failure_count}, 总成功率 {overall_success_rate:.2%}")
        
        # 在网络不稳定情况下，总体成功率应该大于40%
        assert overall_success_rate > 0.40, f"网络弹性不足: {overall_success_rate:.2%}"
        assert total_attempts > 500, f"处理数量不足: {total_attempts}"
    
    def test_resource_exhaustion_recovery(self):
        """测试资源耗尽后的恢复能力"""
        # 创建Mock组件
        sensor_collector = create_mock_sensor_collector(self.config)
        mqtt_client = MockMQTTClient(self.config['mqtt']['device_id'])
        mqtt_client.connect()
        
        # 阶段1: 正常运行
        print("阶段1: 正常运行")
        normal_success = 0
        for _ in range(100):
            test_data = TestDataGenerator.generate_sensor_data()
            sensor_collector.collect_data.return_value = test_data
            
            collected_data = sensor_collector.collect_data()
            if collected_data:
                result = mqtt_client.publish_sensor_data(collected_data)
                if result:
                    normal_success += 1
        
        normal_success_rate = normal_success / 100
        print(f"正常运行成功率: {normal_success_rate:.2%}")
        
        # 阶段2: 模拟资源耗尽
        print("阶段2: 模拟资源耗尽")
        mqtt_client.set_publish_success_rate(0.1)  # 10%成功率，模拟资源耗尽
        
        exhaustion_success = 0
        for _ in range(100):
            test_data = TestDataGenerator.generate_sensor_data()
            sensor_collector.collect_data.return_value = test_data
            
            collected_data = sensor_collector.collect_data()
            if collected_data:
                result = mqtt_client.publish_sensor_data(collected_data)
                if result:
                    exhaustion_success += 1
        
        exhaustion_success_rate = exhaustion_success / 100
        print(f"资源耗尽期间成功率: {exhaustion_success_rate:.2%}")
        
        # 阶段3: 资源恢复
        print("阶段3: 资源恢复")
        mqtt_client.set_publish_success_rate(1.0)  # 恢复100%成功率
        
        recovery_success = 0
        for _ in range(100):
            test_data = TestDataGenerator.generate_sensor_data()
            sensor_collector.collect_data.return_value = test_data
            
            collected_data = sensor_collector.collect_data()
            if collected_data:
                result = mqtt_client.publish_sensor_data(collected_data)
                if result:
                    recovery_success += 1
        
        recovery_success_rate = recovery_success / 100
        print(f"资源恢复后成功率: {recovery_success_rate:.2%}")
        
        # 验证恢复能力
        assert normal_success_rate > 0.95, f"正常运行成功率不足: {normal_success_rate:.2%}"
        assert exhaustion_success_rate < 0.20, f"资源耗尽模拟不准确: {exhaustion_success_rate:.2%}"
        assert recovery_success_rate > 0.95, f"资源恢复能力不足: {recovery_success_rate:.2%}"
        
        # 恢复后的成功率应该接近正常水平
        recovery_ratio = recovery_success_rate / normal_success_rate
        assert recovery_ratio > 0.95, f"恢复效果不佳: {recovery_ratio:.2%}"
    
    def test_long_running_stability(self):
        """测试长时间运行稳定性"""
        # 创建Mock组件
        sensor_collector = create_mock_sensor_collector(self.config)
        mqtt_client = MockMQTTClient(self.config['mqtt']['device_id'])
        mqtt_client.connect()
        
        # 长时间运行测试（10分钟）
        test_duration = 600  # 10分钟
        start_time = time.time()
        
        success_count = 0
        error_count = 0
        performance_samples = []
        
        while time.time() - start_time < test_duration:
            batch_start = time.time()
            batch_success = 0
            
            # 每批处理50条数据
            for _ in range(50):
                try:
                    test_data = TestDataGenerator.generate_sensor_data()
                    sensor_collector.collect_data.return_value = test_data
                    
                    collected_data = sensor_collector.collect_data()
                    if collected_data:
                        result = mqtt_client.publish_sensor_data(collected_data)
                        if result:
                            success_count += 1
                            batch_success += 1
                        else:
                            error_count += 1
                    else:
                        error_count += 1
                        
                except Exception as e:
                    error_count += 1
                    print(f"长时间运行错误: {e}")
            
            batch_duration = time.time() - batch_start
            batch_performance = batch_success / batch_duration  # 成功数/秒
            performance_samples.append(batch_performance)
            
            # 每分钟报告一次状态
            elapsed_minutes = (time.time() - start_time) / 60
            if int(elapsed_minutes) > len(performance_samples) // 10:
                current_success_rate = success_count / (success_count + error_count) if (success_count + error_count) > 0 else 0
                avg_performance = sum(performance_samples) / len(performance_samples)
                print(f"运行 {elapsed_minutes:.1f} 分钟: 成功率 {current_success_rate:.2%}, 平均性能 {avg_performance:.1f} 成功/秒")
        
        # 分析长时间运行结果
        total_attempts = success_count + error_count
        final_success_rate = success_count / total_attempts if total_attempts > 0 else 0
        avg_performance = sum(performance_samples) / len(performance_samples) if performance_samples else 0
        
        # 检查性能稳定性（标准差不应过大）
        if len(performance_samples) > 1:
            import statistics
            performance_std = statistics.stdev(performance_samples)
            performance_cv = performance_std / avg_performance if avg_performance > 0 else 0  # 变异系数
            
            print(f"长时间运行结果: 总成功率 {final_success_rate:.2%}, 平均性能 {avg_performance:.1f} 成功/秒")
            print(f"性能稳定性: 标准差 {performance_std:.2f}, 变异系数 {performance_cv:.2%}")
            
            # 验证长时间运行稳定性
            assert final_success_rate > 0.95, f"长时间运行成功率下降: {final_success_rate:.2%}"
            assert avg_performance > 20, f"长时间运行性能下降: {avg_performance:.1f} 成功/秒"
            assert performance_cv < 0.3, f"性能不稳定: 变异系数 {performance_cv:.2%}"
