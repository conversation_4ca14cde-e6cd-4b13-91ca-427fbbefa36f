"""
传感器模拟对象

提供各种传感器的Mock实现，用于测试时替代真实硬件设备。
"""

import time
import random
from unittest.mock import Mock, MagicMock
from typing import Dict, Any, Optional


class MockCTHSensor:
    """CO2温湿度传感器模拟"""
    
    def __init__(self, device_address=0x01):
        self.device_address = device_address
        self.connected = True
        self.error_rate = 0.0  # 错误率，0.0-1.0
        
    def read_data(self) -> Optional[Dict[str, Any]]:
        """模拟读取传感器数据"""
        if not self.connected or random.random() < self.error_rate:
            return None
            
        return {
            "temperature": round(random.uniform(20.0, 30.0), 1),
            "humidity": round(random.uniform(40.0, 80.0), 1),
            "co2": random.randint(300, 1000)
        }
    
    def set_error_rate(self, rate: float):
        """设置错误率"""
        self.error_rate = max(0.0, min(1.0, rate))
    
    def disconnect(self):
        """模拟断开连接"""
        self.connected = False
    
    def reconnect(self):
        """模拟重新连接"""
        self.connected = True


class MockGPSSensor:
    """GPS传感器模拟"""
    
    def __init__(self, device_address=0x02):
        self.device_address = device_address
        self.connected = True
        self.error_rate = 0.0
        # 默认位置：北京
        self.base_lat = 39.9042
        self.base_lon = 116.4074
        
    def read_data(self) -> Optional[Dict[str, Any]]:
        """模拟读取GPS数据"""
        if not self.connected or random.random() < self.error_rate:
            return None
            
        # 在基础位置附近随机偏移
        lat_offset = random.uniform(-0.01, 0.01)
        lon_offset = random.uniform(-0.01, 0.01)
        
        return {
            "latitude": round(self.base_lat + lat_offset, 6),
            "longitude": round(self.base_lon + lon_offset, 6),
            "altitude": round(random.uniform(50.0, 100.0), 1),
            "satellites": random.randint(4, 12)
        }
    
    def set_location(self, lat: float, lon: float):
        """设置基础位置"""
        self.base_lat = lat
        self.base_lon = lon
    
    def set_error_rate(self, rate: float):
        """设置错误率"""
        self.error_rate = max(0.0, min(1.0, rate))
    
    def disconnect(self):
        """模拟断开连接"""
        self.connected = False
    
    def reconnect(self):
        """模拟重新连接"""
        self.connected = True


class MockWindSpeedSensor:
    """风速传感器模拟"""
    
    def __init__(self, device_address=0x04):
        self.device_address = device_address
        self.connected = True
        self.error_rate = 0.0
        
    def read_data(self) -> Optional[Dict[str, Any]]:
        """模拟读取风速数据"""
        if not self.connected or random.random() < self.error_rate:
            return None
            
        return {
            "wind_speed": round(random.uniform(0.0, 15.0), 1),
            "wind_direction": random.randint(0, 359)
        }
    
    def set_error_rate(self, rate: float):
        """设置错误率"""
        self.error_rate = max(0.0, min(1.0, rate))
    
    def disconnect(self):
        """模拟断开连接"""
        self.connected = False
    
    def reconnect(self):
        """模拟重新连接"""
        self.connected = True


class MockSerial:
    """串口模拟"""
    
    def __init__(self, port="/dev/ttyUSB0", baudrate=9600, timeout=1):
        self.port = port
        self.baudrate = baudrate
        self.timeout = timeout
        self.is_open = True
        self.response_data = {}
        
    def write(self, data: bytes) -> int:
        """模拟写入数据"""
        return len(data)
    
    def read(self, size: int = 1) -> bytes:
        """模拟读取数据"""
        # 根据写入的命令返回相应的响应
        if hasattr(self, '_last_command'):
            return self.response_data.get(self._last_command, b'')
        return b''
    
    def set_response(self, command: bytes, response: bytes):
        """设置命令响应"""
        self.response_data[command] = response
    
    def close(self):
        """关闭串口"""
        self.is_open = False
    
    def open(self):
        """打开串口"""
        self.is_open = True


def create_mock_sensor_collector(config=None):
    """创建传感器收集器模拟对象"""
    collector = Mock()
    collector.running = True
    collector.config = config or {}
    
    # 模拟传感器实例
    collector.cth_sensor = MockCTHSensor()
    collector.gps_sensor = MockGPSSensor()
    collector.wind_sensor = MockWindSpeedSensor()
    
    # 模拟方法
    collector.collect_data.return_value = {
        "timestamp": int(time.time()),
        "datetime": time.strftime("%Y-%m-%d %H:%M:%S"),
        "temperature": 25.5,
        "humidity": 60.2,
        "co2": 400,
        "latitude": 39.9042,
        "longitude": 116.4074,
        "wind_speed": 3.2
    }
    
    collector.start = Mock()
    collector.stop = Mock()
    collector.get_cth_data = Mock(return_value={"temperature": 25.5, "humidity": 60.2, "co2": 400})
    collector.get_gps_data = Mock(return_value={"latitude": 39.9042, "longitude": 116.4074})
    collector.get_wind_speed = Mock(return_value={"wind_speed": 3.2})
    
    return collector
